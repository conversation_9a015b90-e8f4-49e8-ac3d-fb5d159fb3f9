<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GigGenius - Dashboard</title>
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/logo.png') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Add Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <!-- Add Poppins font -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #ffffff;
            --text-gray: #666;
        }

        /* Footer Styles */
        footer {
            background: #003a8c;
            padding: 2rem 5%;
            align-items: center;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 3rem;
        }

        .footer-column h3 {
            margin-bottom: 1rem;
            color: var(--text-light);
        }

        .footer-column a {
            display: block;
            color: var(--text-light);
            text-decoration: none;
            margin-bottom: 0.5rem;
            transition: text-decoration 0.3s ease;
        }

        .footer-column a:hover {
            text-decoration: underline;
        }

        .footer-bottom {
            color: var(--text-light);
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 2rem 0;
            flex-wrap: wrap;
            font-family: 'Poppins', sans-serif;
        }

        .footer-bottom p {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 400;
        }

        .footer-bottom a {
            color: var(--text-light);
            margin: 0 10px;
            text-decoration: none;
        }

        .footer-bottom a:hover {
            text-decoration: underline;
        }

        .footer-bottom .social-icons img {
            width: 24px;
            height: 24px;
            margin: 0 5px;
        }

        .social-icons {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .social-icons a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            margin: 0;
        }

        .social-icons .bi {
            font-size: 1.2rem;
            color: var(--text-light);
            transition: transform 0.3s ease, color 0.3s ease;
        }

        .social-icons a:hover {
            background-color: var(--primary-pink);
            transform: translateY(-3px);
        }

        /* Add responsive styles for footer */
        @media (max-width: 768px) {
            .footer-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 2rem;
            }

            .footer-bottom {
                flex-direction: column;
                gap: 20px;
            }
        }

        @media (max-width: 480px) {
            .footer-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
        }

        body {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.5;
        }

        a {
            text-decoration: none;
            color: inherit;
        }

        ul {
            list-style: none;
        }

        /* Layout */
        .container {
            max-width: 2000px;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 5rem;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .logo img {
            width: 3.5rem;
            height: 3.5rem;
            border-radius: 50%;
            border: 2px solid var(--primary-blue);
            object-fit: cover;
        }

        .logo h1 {
            font-size: 1.7rem;
            font-weight: bold;
            color: #CD208B;
            margin-left: 0.5rem;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            color: #004AAD;
            text-decoration: none;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .nav-links a:hover {
            color: #CD208B;
        }

        .nav-dropdown {
            position: relative;
            display: inline-block;
        }

        .nav-dropbtn {
            font-weight: 500;
            font-size: 1.1rem;
            color: #004AAD;
            background: none;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0;
        }

        .nav-dropbtn:hover {
            color: #CD208B;
        }

        .nav-dropdown-content {
            display: none;
            position: absolute;
            background-color: #fff;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            top: 100%;
            left: 0;
        }

        .nav-dropdown-content a {
            color: #004AAD;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            font-size: 1rem;
        }

        .nav-dropdown-content a:hover {
            background-color: #f9f9f9;
            color: #CD208B;
        }

        .nav-dropdown:hover .nav-dropdown-content {
            display: block;
        }

        /* Right section container */
        .right-section {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        /* Search container */
        .search-container {
            display: flex;
            align-items: center;
        }

        .search-type-select {
            position: relative;
        }

        .search-type-button {
            height: 2.5rem;
            background: white;
            border: 1px solid #004AAD;
            border-right: none;
            border-radius: 8px 0 0 8px;
            padding: 0 1rem;
            color: #004AAD;
            font-size: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .search-type-button:hover {
            background-color: #f0f7ff;
        }

        .search-bar {
            height: 2.5rem;
            display: flex;
            align-items: center;
            background: white;
            border: 1px solid #004AAD;
            border-radius: 0 8px 8px 0;
            width: 220px;
        }

        .search-bar input {
            border: none;
            outline: none;
            padding: 0 0.5rem;
            width: 100%;
            height: 100%;
            font-size: 1rem;
        }

        .search-bar .icon {
            color: #004AAD;
            padding: 0 0.5rem;
            cursor: pointer;
        }

        .search-bar .icon:hover {
            color: #CD208B;
        }

        /* Auth buttons container */
        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        /* Notification icon */
        .notification-icon {
            position: relative;
            cursor: pointer;
        }

        .notification-icon i {
            font-size: 1.5rem;
            color: #004AAD;
        }

        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: #CD208B;
            color: white;
            border-radius: 50%;
            padding: 0.1rem 0.4rem;
            font-size: 0.8rem;
            min-width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Profile button */
        .profile-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid #004AAD;
        }

        .profile-button img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Profile dropdown */
        .profile-dropdown {
            position: relative;
            display: inline-block;
        }

        .profile-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 50px;
            background-color: #fff;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
        }

        .profile-dropdown-content a {
            color: #004AAD;
            padding: 12px 16px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1rem;
        }

        .profile-dropdown-content a i {
            width: 20px;
            text-align: center;
        }

        .profile-dropdown-content a:hover {
            background-color: #f9f9f9;
            color: #CD208B;
        }

        .dropdown-divider {
            height: 1px;
            background-color: #eee;
            margin: 8px 0;
        }

        .logout-option {
            color: #dc3545 !important;
        }

        .logout-option:hover {
            background-color: #fff5f5 !important;
            color: #dc3545 !important;
        }

        /* Show dropdown on click */
        .profile-dropdown-content.show {
            display: block;
        }

        /* Notification Dropdown Styles */
        .notification-dropdown {
            position: relative;
        }

        .notification-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: white;
            min-width: 320px;
            max-height: 400px;
            overflow-y: auto;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            padding: 0;
            z-index: 100;
            display: none;
            margin-top: 0.5rem;
            border: 1px solid #e5e7eb;
        }

        .notification-dropdown-menu.active {
            display: block;
        }

        .notification-header {
            padding: 1rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-header h3 {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            color: #333;
        }

        .notification-header .mark-all-read {
            font-size: 0.75rem;
            color: #064dac;
            cursor: pointer;
            background: none;
            border: none;
            padding: 0;
        }

        .notification-header .mark-all-read:hover {
            text-decoration: underline;
        }

        .notification-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .notification-item {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
        }

        .notification-item:hover {
            background-color: #f9fafb;
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-item.unread {
            background-color: #f0f7ff;
        }

        .notification-item.unread:hover {
            background-color: #e1f0ff;
        }

        .notification-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .notification-icon i {
            color: #064dac;
            font-size: 1rem;
        }

        .notification-content {
            flex: 1;
        }

        .notification-text {
            font-size: 0.875rem;
            color: #333;
            margin-bottom: 0.25rem;
            line-height: 1.4;
        }

        .notification-text strong {
            color: #003a8c;
            font-weight: 600;
        }

        .notification-time {
            font-size: 0.75rem;
            color: #6b7280;
        }

        .notification-footer {
            padding: 0.75rem 1rem;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }

        .notification-footer a {
            font-size: 0.875rem;
            color: #064dac;
            text-decoration: none;
        }

        .notification-footer a:hover {
            text-decoration: underline;
        }

        .no-notifications {
            padding: 2rem 1rem;
            text-align: center;
            color: #6b7280;
            font-size: 0.875rem;
        }

        .mobile-menu-btn {
            background: none;
            border: none;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .mobile-menu-btn:hover {
            background-color: #f3f4f6;
        }

        .mobile-menu {
            display: none;
            position: absolute;
            top: 60px;
            right: 1rem;
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            width: 200px;
            z-index: 20;
            border: 1px solid #e5e7eb;
        }

        .mobile-menu.active {
            display: block;
        }

        .mobile-menu a {
            display: block;
            padding: 0.75rem 1rem;
            color: #333;
            font-size: 0.875rem;
            border-bottom: 1px solid #f3f4f6;
        }

        .mobile-menu a:last-child {
            border-bottom: none;
        }

        .mobile-menu a:hover {
            background-color: #f9fafb;
        }

        /* Main Content */
        .main-content {
            padding: 1.5rem 1rem 3rem;
        }

        /* Profile Section */
        .profile-section {
            padding: 1.5rem 0;
        }

        .profile-card {
    background-color: white;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.3s ease;
}

.profile-card:hover {
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

        .profile-content {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 2rem;
    padding: 2rem;
}

        .profile-info {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 2rem;
            padding: 1.5rem;
        }

        .profile-avatar {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 3px solid rgba(212, 27, 140, 0.2);
    overflow: hidden;
    flex-shrink: 0;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

        .profile-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-details {
            flex-grow: 1;
        }

        .profile-name {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .profile-name span {
            color: #d41b8c;
        }

        .profile-meta {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            margin-top: 0.25rem;
            color: #6b7280;
            font-size: 0.875rem;
        }

        .profile-meta-item {
            display: flex;
            align-items: center;
            gap: 0.375rem;
        }

        .profile-meta-item i {
            color: #064dac;
        }

        .profile-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 0.75rem;
        }

        .profile-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
            pointer-events: auto;
            z-index: 10;
            position: relative;
        }

        .btn-outline {
            background-color: transparent;
            border: 1px solid #e5e7eb;
            color: #333;
        }

        .btn-outline:hover {
            background-color: #f9fafb;
        }

        .btn-outline.active {
            background-color: #064dac;
            color: white;
            border-color: #064dac;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .stat-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
    text-align: center;
    background-color: white;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.stat-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: #d41b8c;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

        /* Introduction Section */
        .introduction-section {
    margin-top: 1.5rem;
    padding: 1.75rem;
    background-color: white;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.introduction-section h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #003a8c;
}

.truncated-text {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin: 0;
    line-height: 1.6;
    color: #4b5563;
    font-size: 1rem;
}

        .text-wrapper {
            display: flex;
            align-items: flex-end;
        }

        .truncated-text {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            margin: 0;
            line-height: 1.5;
            color: #6b7280;
        }

        .show-more-btn {
            color: #064dac;
            font-weight: 500;
            white-space: nowrap;
            margin-left: 0.25rem;
        }

        .show-more-btn:hover {
            text-decoration: underline;
        }

        /* Quote Slider */
        .quote-slider {
            margin: 2rem 0;
            padding: 2rem;
            background-color: #f3f4f6;
            border-radius: 8px;
            text-align: center;
        }

        .quote-navigation {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 2rem;
        }

        .arrow {
            font-size: 1.5rem;
            color: #064dac;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .arrow:hover {
            transform: scale(1.2);
        }

        .quote {
            font-size: 1.5rem;
            font-weight: 600;
            line-height: 1.4;
            transition: opacity 0.3s ease;
        }

        .main-text {
            display: block;
            color: #064dac;
        }

        .sub-text {
            display: block;
            color: #d41b8c;
            margin-top: 0.5rem;
        }

        .quote.fade {
            opacity: 0;
        }

        /* Dashboard Layout */
        .dashboard-content {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        /* Filters Sidebar */
        .filters {
            background-color: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 1.5rem;
        }

        .filters-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.25rem;
        }

        .filters-header h2 {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .clear-filters-btn {
            display: flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            background-color: transparent;
            border: 1px solid #064dac;
            color: #064dac;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .clear-filters-btn:hover {
            background-color: #064dac;
            color: white;
        }

        .filter-group {
            margin-bottom: 1.25rem;
        }

        .filter-group label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .filter-select, .filter-input {
            width: 100%;
            padding: 0.625rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            color: #333;
        }

        .filter-select:focus, .filter-input:focus {
            outline: none;
            border-color: #064dac;
            box-shadow: 0 0 0 2px rgba(6, 77, 172, 0.1);
        }

        .filter-section {
            margin-bottom: 1.25rem;
            border-top: 1px solid #e5e7eb;
            padding-top: 1.25rem;
        }

        .filter-section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            padding: 0.5rem 0;
        }

        .filter-section-header h3 {
            font-size: 1rem;
            font-weight: 600;
        }

        .filter-section-header i {
            color: #064dac;
            transition: transform 0.3s ease;
        }

        .filter-section-header.active i {
            transform: rotate(180deg);
        }

        .filter-section-content {
            display: none;
            padding-top: 0.75rem;
        }

        .filter-section-content.show {
            display: block;
        }

        .toggle-container {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.75rem;
        }

        .toggle-label {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .toggle-switch {
            position: relative;
            width: 40px;
            height: 20px;
            background-color: #e74c3c;
            border-radius: 20px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .toggle-switch.on {
            background-color: #2ecc71;
        }

        .toggle-switch .slider {
            position: absolute;
            width: 16px;
            height: 16px;
            background-color: white;
            border-radius: 50%;
            top: 2px;
            left: 2px;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.on .slider {
            left: calc(100% - 18px);
        }

        .visibility-pill {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            background-color: #e0f7fa;
            color: #064dac;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .my-proposal-link {
            display: block;
            padding: 0.75rem 1rem;
            background-color: white;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            color: #333;
            font-size: 0.875rem;
            transition: all 0.2s;
        }

        .my-proposal-link:hover {
            border-color: #064dac;
            background-color: #f9fafb;
        }

        .my-proposal-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .my-proposal-item i {
            color: #064dac;
            font-size: 0.75rem;
        }

        /* Search and Jobs */
        .jobs-container {
            flex: 1;
        }

        .search-container {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .search-input-wrapper {
            position: relative;
            width: 100%;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem 1rem;
            padding-right: 2.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
        }

        .search-input:focus {
            outline: none;
            border-color: #064dac;
            box-shadow: 0 0 0 2px rgba(6, 77, 172, 0.1);
        }

        .search-icon-wrapper {
            position: absolute;
            top: 0;
            right: 0;
            height: 100%;
            width: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .search-icon {
            color: #6b7280;
        }

        .no-results {
            text-align: center;
            padding: 3rem 1rem;
            color: #6b7280;
            background-color: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        /* Job Cards - Row Based Layout */
        .jobs-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .job-card {
    background-color: white;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    overflow: hidden;
}

.job-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
    border-color: rgba(212, 27, 140, 0.3);
}

        .job-header {
            padding: 1.25rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .job-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #333;
        }

        .job-body {
            padding: 1.25rem;
        }

        .job-description {
            color: #6b7280;
            margin-bottom: 1.25rem;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .job-details {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.75rem;
        }

        .job-detail {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            color: #6b7280;
        }

        .job-detail i {
            color: #064dac;
            width: 16px;
            text-align: center;
        }

        .job-footer {
            padding: 1.25rem;
            border-top: 1px solid #e5e7eb;
        }

        .view-btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background-color: #d41b8c;
    color: white;
    border: none;
    border-radius: 50px;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-btn:hover {
    background-color: #b91c77;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(212, 27, 140, 0.3);
}

        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 2rem;
            gap: 0.5rem;
        }

        .page-item {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s;
        }

        .page-item:hover {
            border-color: #064dac;
            color: #064dac;
        }

        .page-item.active {
            background-color: #064dac;
            border-color: #064dac;
            color: white;
        }

        .page-arrow {
            color: #6b7280;
        }

        .page-arrow:hover {
            color: #064dac;
        }

        /* Content Sections */
        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        /* My Applications Styles */
        .jobs-header {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .jobs-title {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .jobs-filters {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .filter-select, .filter-input {
            padding: 0.625rem 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            width: 100%;
        }

        .jobs-grid {
            display: grid;
            gap: 1.5rem;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-pending {
            background-color: #fff7ed;
            color: #c2410c;
            border: 1px solid #fdba74;
        }

        .status-accepted {
            background-color: #ecfdf5;
            color: #047857;
            border: 1px solid #6ee7b7;
        }

        .status-rejected {
            background-color: #fef2f2;
            color: #b91c1c;
            border: 1px solid #fca5a5;
        }

        .status-review {
            background-color: #eff6ff;
            color: #1d4ed8;
            border: 1px solid #93c5fd;
        }

        .save-btn {
            display: flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            background-color: transparent;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }

        .save-btn:hover {
            background-color: #f3f4f6;
        }

        .save-btn.saved {
            color: #2563eb;
        }

        .save-btn-text {
            display: none;
        }

        /* Job Offers Styles */
        .add-to-cart {
            position: absolute;
            top: 1rem;
            right: 1rem;
            display: flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            background-color: #f3f4f6;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .add-to-cart:hover {
            background-color: #e5e7eb;
        }

        .add-to-cart.added {
            background-color: #dcfce7;
            border-color: #86efac;
            color: #166534;
        }

        /* My Clients Styles */
        .clients-section {
            padding: 1.5rem 0;
        }

        .clients-header {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .clients-title {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .clients-search {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .search-input {
            padding: 0.625rem 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            width: 100%;
        }

        .clients-scroll {
            overflow-x: auto;
            padding-bottom: 1rem;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: thin;
            scrollbar-color: #d1d5db transparent;
        }

        .clients-scroll::-webkit-scrollbar {
            height: 8px;
        }

        .clients-scroll::-webkit-scrollbar-track {
            background: transparent;
        }

        .clients-scroll::-webkit-scrollbar-thumb {
            background-color: #d1d5db;
            border-radius: 20px;
        }

        .clients-container {
            display: flex;
            gap: 1.5rem;
            padding: 0.5rem 0.25rem;
        }

        .client-card {
            background-color: white;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            position: relative;
            min-width: 300px;
            max-width: 400px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .client-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }

        .client-header {
            padding: 1.25rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .client-title {
            font-weight: 600;
            font-size: 1.125rem;
        }

        .favorite-btn {
            position: absolute;
            top: 1rem;
            right: 1rem;
            display: flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            background-color: #f3f4f6;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .favorite-btn:hover {
            background-color: #e5e7eb;
        }

        .favorite-btn.favorited {
            background-color: #dcfce7;
            border-color: #86efac;
            color: #166534;
        }

        .client-body {
            padding: 1.25rem;
        }

        .client-description {
            color: #6b7280;
            margin-bottom: 1.25rem;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            font-size: 0.9375rem;
        }

        .client-details {
            display: grid;
            grid-template-columns: 1fr;
            gap: 0.75rem;
        }

        .client-detail {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }

        .client-detail i {
            color: #6b7280;
            width: 16px;
            text-align: center;
        }

        .client-footer {
            padding: 1.25rem;
            border-top: 1px solid #e5e7eb;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            animation: fadeIn 0.3s;
        }

        .modal-content {
            background-color: #fff;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            position: relative;
            animation: slideIn 0.3s;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
        }

        .modal-description {
            color: #6b7280;
            margin-bottom: 20px;
        }

        .close-modal {
            font-size: 28px;
            font-weight: bold;
            color: #666;
            cursor: pointer;
        }

        .close-modal:hover {
            color: #000;
        }

        .radio-option {
            display: block;
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .radio-option:hover {
            background-color: #f9fafb;
        }

        .radio-input {
            margin-right: 10px;
        }

        .radio-label {
            font-weight: 600;
            margin-left: 0.5rem;
        }

        .info {
            margin-left: 1.5rem;
            color: #6b7280;
            margin-top: 0.5rem;
        }

        .buttons {
            margin-top: 2rem;
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }

        .buttons button {
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
        }

        .buttons .cancel {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
        }

        .buttons .save {
            background: #d41b8c;
            color: white;
            border: none;
        }

        /* Modal Content Styles */
        .modal-jobs-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            max-height: 400px;
            overflow-y: auto;
        }

        .modal-job-card {
            background-color: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 1rem;
            transition: all 0.2s;
        }

        .modal-job-card:hover {
            background-color: #f3f4f6;
        }

        .modal-job-title {
            font-size: 1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .modal-job-description {
            color: #6b7280;
            font-size: 0.875rem;
            margin-bottom: 0.75rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .modal-job-details {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            font-size: 0.75rem;
            color: #6b7280;
        }

        .modal-job-detail {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .modal-job-detail i {
            color: #064dac;
        }

        .modal-clients-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            max-height: 400px;
            overflow-y: auto;
        }

        .modal-client-card {
            background-color: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            padding: 1rem;
            transition: all 0.2s;
        }

        .modal-client-card:hover {
            background-color: #f3f4f6;
        }

        .modal-client-title {
            font-size: 1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .modal-client-description {
            color: #6b7280;
            font-size: 0.875rem;
            margin-bottom: 0.75rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .modal-client-details {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            font-size: 0.75rem;
            color: #6b7280;
        }

        .modal-client-detail {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .modal-client-detail i {
            color: #064dac;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(-20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        /* Responsive Styles */
        @media (min-width: 640px) {
            .header-content {
                padding: 0 1.5rem;
            }

            .main-content {
                padding: 1.5rem 1.5rem 3rem;
            }

            .profile-info {
                flex-direction: row;
                align-items: center;
            }

            .profile-meta {
                flex-direction: row;
                gap: 1rem;
            }

            .jobs-filters {
                flex-direction: row;
                gap: 0.75rem;
            }

            .filter-select, .filter-input {
                width: auto;
            }

            .job-details {
                grid-template-columns: repeat(2, 1fr);
            }

            .save-btn-text {
                display: inline;
            }

            .clients-search {
                flex-direction: row;
                gap: 0.75rem;
            }

            .search-input {
                width: auto;
            }

            .client-details {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 768px) {
            .header-content {
                padding: 0 1.5rem;
            }

            .nav-links {
                display: flex;
            }

            .mobile-menu-btn {
                display: none;
            }

            .profile-content {
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
            }

            .dashboard-content {
                flex-direction: row;
            }

            .filters {
                width: 280px;
                align-self: flex-start;
                position: sticky;
                top: 80px;
            }

            .jobs-container {
                flex: 1;
                margin-left: 1.5rem;
            }

            .stats-grid {
                grid-template-columns: repeat(4, 1fr);
            }

            .jobs-header {
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
            }

            .jobs-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .clients-header {
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
            }
        }

        @media (min-width: 1024px) {
            .header-content {
                padding: 0 2rem;
            }

            .main-content {
                padding: 2rem 2rem 4rem;
            }

            .filters {
                width: 320px;
            }

            .client-card {
                min-width: 350px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Navbar -->
        <nav class="navbar">
            <div class="navbar-left">
                <a href="{{ url_for('landing_page') }}" class="logo">
                    <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                    <h1>GigGenius</h1>
                </a>
                <div class="nav-links">
                    <a href="{{ url_for('genius_page') }}">Find Gigs</a>
                    <a href="{{ url_for('my_proposal') }}">Proposals</a>

                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Contracts
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('tracker') }}">Log Works</a>
                        </div>
                    </div>

                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Earnings
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('billing_and_earnings') }}">Billings and Earnings</a>
                            <a href="{{ url_for('withdraw_earnings') }}">Withdraw Earnings</a>
                            <a href="{{ url_for('tax_info') }}">Tax Info</a>
                        </div>
                    </div>

                    <a href="{{ url_for('messages') }}">Messages</a>
                </div>
            </div>
            <div class="right-section">
                <div class="search-container">
                    <div class="search-type-select">
                        <button class="search-type-button">
                            <span>Gigs</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                    <div class="search-bar">
                        <input type="text" placeholder="Search for gigs...">
                        <div class="icon">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                </div>
                <div class="auth-buttons">
                    <div class="notification-icon">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </div>
                    <div class="profile-dropdown">
                        <div class="profile-button">
                            <img src="{{ genius.profile_picture_url }}" alt="Profile Picture">
                        </div>
                        <div class="profile-dropdown-content">
                            <a href="{{ url_for('genius_profile') }}">
                                <i class="fas fa-user"></i> My Profile
                            </a>
                            <a href="{{ url_for('withdraw_earnings', section='profile-settings') }}">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="{{ url_for('logout') }}" class="logout-option">
                                <i class="fas fa-sign-out-alt"></i> Log Out
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <div class="mobile-menu" id="mobileMenu">
            <a href="{{ url_for('genius_page') }}">Find Gigs</a>
            <a href="{{ url_for('my_proposal') }}">Proposals</a>
            <a href="#">Contracts</a>
            <a href="{{ url_for('tracker') }}">Log Works</a>
            <a href="#">Earnings</a>
            <a href="{{ url_for('billing_and_earnings') }}">Billings and Earnings</a>
            <a href="{{ url_for('withdraw_earnings') }}">Withdraw Earnings</a>
            <a href="{{ url_for('tax_info') }}">Tax Info</a>
            <a href="{{ url_for('messages') }}">Messages</a>
            <a href="{{ url_for('genius_profile') }}">Profile</a>
        </div>

        <script>
        // Modal Functions (Global scope for onclick handlers)
        function openModal(modalId) {
            console.log('Opening modal:', modalId); // Debug log
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'block';
                console.log('Modal opened successfully'); // Debug log
            } else {
                console.error('Modal not found:', modalId); // Debug log
            }
        }

        function closeModal(modalId) {
            console.log('Closing modal:', modalId); // Debug log
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'none';
                console.log('Modal closed successfully'); // Debug log
            } else {
                console.error('Modal not found:', modalId); // Debug log
            }
        }

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        });
        </script>

        <script>
            // Navbar dropdown functionality
            document.addEventListener('DOMContentLoaded', function() {
                // Profile dropdown functionality
                const profileButton = document.querySelector('.profile-button');
                const profileDropdown = document.querySelector('.profile-dropdown-content');

                if (profileButton && profileDropdown) {
                    profileButton.addEventListener('click', function(e) {
                        e.stopPropagation();
                        profileDropdown.classList.toggle('show');
                    });
                }

                // Close dropdowns when clicking outside
                window.addEventListener('click', function(e) {
                    // Close profile dropdown
                    if (!e.target.closest('.profile-dropdown')) {
                        if (profileDropdown) {
                            profileDropdown.classList.remove('show');
                        }
                    }

                    // Close nav dropdowns
                    if (!e.target.matches('.nav-dropbtn')) {
                        const dropdowns = document.getElementsByClassName('nav-dropdown-content');
                        for (let dropdown of dropdowns) {
                            if (dropdown.classList.contains('show')) {
                                dropdown.classList.remove('show');
                            }
                        }
                    }
                });

                // Add click event to dropdown buttons
                const dropBtns = document.querySelectorAll('.nav-dropbtn');
                dropBtns.forEach(btn => {
                    btn.addEventListener('click', function(e) {
                        e.preventDefault();
                        const content = this.nextElementSibling;
                        content.classList.toggle('show');
                    });
                });
            });
        </script>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Profile Section -->
            <section class="profile-section">
                <div class="profile-card">
                    <div class="profile-content">
                        <div class="profile-info">
                            <a href="{{ url_for('genius_profile') }}" class="profile-avatar" style="cursor: pointer; text-decoration: none;">
                                <img src="{{ genius.profile_picture_url }}" alt="{{ genius.first_name }} {{ genius.last_name }}">
                            </a>
                            <div class="profile-details">
                                <h2 class="profile-name">
                                    Welcome <a href="{{ url_for('genius_profile') }}" style="text-decoration: none; color: inherit; cursor: pointer;"><span>{{ genius.first_name }} {{ genius.last_name }}</span></a>
                                </h2>
                                <div class="profile-meta">
                                    <div class="profile-meta-item">
                                        <i class="fas fa-briefcase"></i>
                                        <span>{{ genius.position }}</span>
                                    </div>
                                    <div class="profile-meta-item">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <span>{{ genius.country }}</span>
                                    </div>
                                </div>
                                <div class="profile-actions">
                                    <button class="profile-btn btn-outline active" id="dashboardBtn">
                                        <i class="fas fa-home"></i>
                                        <span>Dashboard</span>
                                    </button>
                                    <a href="{{ url_for('genius_profile') }}" class="profile-btn btn-outline">
                                        <i class="fas fa-user"></i>
                                        <span>Profile</span>
                                    </a>
                                    <button class="profile-btn btn-outline" id="myApplicationsBtn" onclick="openModal('myApplicationsModal')">
                                        <i class="fas fa-folder"></i>
                                        <span>My Applications</span>
                                    </button>
                                    <button class="profile-btn btn-outline" id="jobOffersBtn" onclick="openModal('jobOffersModal')">
                                        <i class="fas fa-bell"></i>
                                        <span>Job Offers</span>
                                    </button>
                                    <button class="profile-btn btn-outline" id="myClientsBtn" onclick="openModal('myClientsModal')">
                                        <i class="fas fa-star"></i>
                                        <span>My Clients</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Stats Section -->
<section class="stats-section">
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value">${{ genius.hourly_rate|default('0') }}</div>
            <div class="stat-label">Hourly Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">$0</div>
            <div class="stat-label">Total Earnings</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">0</div>
            <div class="stat-label">Hired</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">0</div>
            <div class="stat-label">Completed Jobs</div>
        </div>
    </div>
</section>

            <!-- Introduction Section -->
            <section class="introduction-section" id="introductionSection">
                <h3>Introduction</h3>
                <div class="text-wrapper">
                    <p class="truncated-text">
                        "{{ genius.introduction|default('No introduction provided yet.') }}"
                    </p>
                    <a href="{{ url_for('genius_profile') }}#introduction" class="show-more-btn" style="text-decoration: none; color: #d12b82; cursor: pointer;">...Show More</a>
                </div>
            </section>

            <!-- Quote Slider -->
            <div class="quote-slider" id="quoteSlider">
                <div class="quote-navigation">
                    <span class="arrow left" onclick="changeQuote(-1)">
                        <i class="fas fa-chevron-left"></i>
                    </span>
                    <h1 class="quote" id="currentQuote">
                        <span class="main-text">Getting a job shouldn't be expensive.</span>
                        <span class="sub-text">No connect or bids needed.</span>
                    </h1>
                    <span class="arrow right" onclick="changeQuote(1)">
                        <i class="fas fa-chevron-right"></i>
                    </span>
                </div>
            </div>

            <!-- Dashboard Content (Default) -->
            <div id="dashboardContent" class="content-section active">
                <div class="dashboard-content">
                    <!-- Filters Sidebar -->
                    <aside class="filters">
                        <div class="filters-header">
                            <h2>Filter Gigs</h2>
                            <button id="clearFilters" class="clear-filters-btn">
                                <i class="fas fa-times"></i> Clear Filters
                            </button>
                        </div>

                        <div class="filter-group">
                            <select id="locationFilter" class="filter-select">
                                <option value="">Select Location</option>
                                <option value="Remote">Remote</option>
                                <option value="United States">United States</option>
                                <option value="United Kingdom">United Kingdom</option>
                                <option value="Canada">Canada</option>
                                <option value="Australia">Australia</option>
                                <option value="Germany">Germany</option>
                                <option value="France">France</option>
                                <option value="India">India</option>
                                <option value="Nigeria">Nigeria</option>
                                <option value="Philippines">Philippines</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <select id="categoryFilter" class="filter-select">
                                <option value="">Select Category</option>
                                <optgroup label="Programming & Development">
                                    <option value="Web Development">Web Development</option>
                                    <option value="Mobile Development">Mobile Development</option>
                                    <option value="Full Stack">Full Stack Development</option>
                                    <option value="Frontend">Frontend Development</option>
                                    <option value="Backend">Backend Development</option>
                                </optgroup>
                                <optgroup label="Design & Creative">
                                    <option value="UI/UX">UI/UX Design</option>
                                    <option value="Graphic Design">Graphic Design</option>
                                    <option value="Logo Design">Logo Design</option>
                                    <option value="Brand Design">Brand Design</option>
                                </optgroup>
                                <optgroup label="Digital Marketing">
                                    <option value="SEO">SEO Optimization</option>
                                    <option value="Social Media">Social Media Marketing</option>
                                    <option value="Content Marketing">Content Marketing</option>
                                    <option value="Email Marketing">Email Marketing</option>
                                </optgroup>
                                <optgroup label="Writing & Content">
                                    <option value="Content Writing">Content Writing</option>
                                    <option value="Copywriting">Copywriting</option>
                                    <option value="Technical Writing">Technical Writing</option>
                                    <option value="Blog Writing">Blog Writing</option>
                                </optgroup>
                            </select>
                        </div>

                        <div class="filter-group">
                            <select id="typeFilter" class="filter-select">
                                <option value="">Select Type</option>
                                <option value="Full-Time">Full Time</option>
                                <option value="Part-Time">Part Time</option>
                                <option value="Freelance">Freelance</option>
                                <option value="Contract">Contract</option>
                                <option value="Intern">Intern</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="priceFilter">Min. Rate/Hour ($)</label>
                            <input type="number" id="priceFilter" class="filter-input" min="0" placeholder="Enter minimum rate">
                        </div>

                        <!-- Promote Adds Section -->
                        <div class="filter-section">
                            <div class="filter-section-header" id="promoteHeader">
                                <h3>Availability</h3>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="filter-section-content" id="promoteContent">
                                <div class="toggle-container">
                                    <div class="toggle-switch" id="availabilityToggle">
                                        <div class="slider"></div>
                                    </div>
                                    <span class="toggle-label">Availability Badge <span id="toggleStatus">OFF</span></span>
                                </div>
                            </div>
                        </div>

                        <!-- Preferences Section -->
                        <div class="filter-section">
                            <div class="filter-section-header" id="preferencesHeader">
                                <h3>Preferences</h3>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="filter-section-content" id="preferencesContent">
                                <div class="filter-group">
                                    <div class="visibility-header">
                                        <label>Profile Visibility</label>
                                        <a href="#" class="edit-visibility-btn" id="editVisibilityBtn">
                                            <i class="fas fa-pencil-alt"></i>
                                        </a>
                                    </div>
                                    <div class="visibility-pill">Public</div>
                                </div>

                                <div class="filter-group">
                                    <div class="hours-header">
                                        <label>Hours per Week</label>
                                        <a href="#" class="edit-hours-btn">
                                            <i class="fas fa-pencil-alt"></i>
                                        </a>
                                    </div>
                                    <div class="visibility-pill">More than 20 hrs/week</div>
                                </div>
                            </div>
                        </div>

                        <!-- Proposal Section -->
                        <div class="filter-section">
                            <div class="filter-section-header" id="proposalHeader">
                                <h3>Proposal</h3>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="filter-section-content" id="proposalContent">
                                <a href="My_proposal.html" class="my-proposal-link">
                                    <div class="my-proposal-item">
                                        <span>My Proposal</span>
                                        <i class="fas fa-chevron-right"></i>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </aside>

                    <!-- Jobs Container -->
                    <div class="jobs-container">
                        <div class="search-container">
                            <div class="search-input-wrapper">
                                <input type="text" class="search-input" id="jobSearch" placeholder="Search for jobs, skills, or keywords...">
                                <div class="search-icon-wrapper">
                                    <i class="fas fa-search search-icon"></i>
                                </div>
                            </div>
                        </div>

                        <div id="noResults" class="no-results" style="display: none;">
                            No gigs match your current filters. Try adjusting your search criteria.
                        </div>

                        <!-- Job Cards - Row Based Layout -->
                        <div class="jobs-list" id="jobsList">
                            <!-- Page 1 -->
                            <div class="page" data-page="1">
                                <!-- Job Card 1 -->
                                <div class="job-card" data-location="New York, USA" data-category="Frontend" data-type="Full-Time" data-price="65">
                                    <div class="job-header">
                                        <h3 class="job-title">Senior Frontend Developer</h3>
                                    </div>
                                    <div class="job-body">
                                        <p class="job-description">Frontend developer for enterprise SaaS platform. React and TypeScript expertise required.</p>
                                        <div class="job-details">
                                            <div class="job-detail">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>New York, USA</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-briefcase"></i>
                                                <span>Long-term</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-user-graduate"></i>
                                                <span>Senior</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-suitcase"></i>
                                                <span>Full-Time</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-clock"></i>
                                                <span>Monthly</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-dollar-sign"></i>
                                                <span>$65/hr</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-footer">
                                        <button class="view-btn">View</button>
                                    </div>
                                </div>

                                <div class="job-card" data-location="Berlin, Germany" data-category="Frontend" data-type="Part-Time" data-price="50">
                                    <div class="job-header">
                                        <h3 class="job-title">Frontend Engineer (Part-Time)</h3>
                                    </div>
                                    <div class="job-body">
                                        <p class="job-description">Join a dynamic team to build user-friendly interfaces using Vue.js and JavaScript. Part-time role with flexible hours.</p>
                                        <div class="job-details">
                                            <div class="job-detail">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Berlin, Germany</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-briefcase"></i>
                                                <span>6 Months</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-user-graduate"></i>
                                                <span>Mid-Level</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-suitcase"></i>
                                                <span>Part-Time</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-clock"></i>
                                                <span>Hourly</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-dollar-sign"></i>
                                                <span>$50/hr</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-footer">
                                        <button class="view-btn">View</button>
                                    </div>
                                </div>

                                <div class="job-card" data-location="London, UK" data-category="Frontend" data-type="Freelance" data-price="60">
                                    <div class="job-header">
                                        <h3 class="job-title">Freelance Frontend Developer</h3>
                                    </div>
                                    <div class="job-body">
                                        <p class="job-description">Freelance opportunity to work on a high-traffic e-commerce platform. Expertise in Angular and JavaScript required.</p>
                                        <div class="job-details">
                                            <div class="job-detail">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>London, UK</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-briefcase"></i>
                                                <span>3 Months</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-user-graduate"></i>
                                                <span>Senior</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-suitcase"></i>
                                                <span>Freelance</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-clock"></i>
                                                <span>Hourly</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-dollar-sign"></i>
                                                <span>$60/hr</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-footer">
                                        <button class="view-btn">View</button>
                                    </div>
                                </div>

                                <div class="job-card" data-location="Toronto, Canada" data-category="Frontend" data-type="Full-Time" data-price="70">
                                    <div class="job-header">
                                        <h3 class="job-title">Frontend Developer (Full-Time)</h3>
                                    </div>
                                    <div class="job-body">
                                        <p class="job-description">Develop and maintain web applications for a leading fintech company. Strong skills in React and Redux required.</p>
                                        <div class="job-details">
                                            <div class="job-detail">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Toronto, Canada</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-briefcase"></i>
                                                <span>Long-term</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-user-graduate"></i>
                                                <span>Mid-Level</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-suitcase"></i>
                                                <span>Full-Time</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-clock"></i>
                                                <span>Monthly</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-dollar-sign"></i>
                                                <span>$70/hr</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-footer">
                                        <button class="view-btn">View</button>
                                    </div>
                                </div>

                                <div class="job-card" data-location="Remote" data-category="Frontend" data-type="Full-Time" data-price="80">
                                    <div class="job-header">
                                        <h3 class="job-title">Remote Frontend Developer</h3>
                                    </div>
                                    <div class="job-body">
                                        <p class="job-description">Work remotely on a global SaaS product. Expertise in React, TypeScript, and GraphQL required.</p>
                                        <div class="job-details">
                                            <div class="job-detail">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Remote</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-briefcase"></i>
                                                <span>Long-term</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-user-graduate"></i>
                                                <span>Senior</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-suitcase"></i>
                                                <span>Full-Time</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-clock"></i>
                                                <span>Monthly</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-dollar-sign"></i>
                                                <span>$80/hr</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-footer">
                                        <button class="view-btn">View</button>
                                    </div>
                                </div>

                                <!-- Job Card 6 -->
                                <div class="job-card" data-location="London, UK" data-category="Backend" data-type="Remote" data-price="70">
                                    <div class="job-header">
                                        <h3 class="job-title">Python Backend Developer</h3>
                                    </div>
                                    <div class="job-body">
                                        <p class="job-description">Backend developer for fintech application. Python, Django, and AWS experience required.</p>
                                        <div class="job-details">
                                            <div class="job-detail">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>London, UK</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-briefcase"></i>
                                                <span>Permanent</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-user-graduate"></i>
                                                <span>Mid-Senior</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-suitcase"></i>
                                                <span>Remote</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-clock"></i>
                                                <span>Monthly</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-dollar-sign"></i>
                                                <span>$70/hr</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-footer">
                                        <button class="view-btn">View</button>
                                    </div>
                                </div>
                            </div>

                            <!-- Page 2 -->
                            <div class="page" data-page="2" style="display: none;">
                                <!-- Job Card 7 -->
                                <div class="job-card" data-location="Austin, USA" data-category="Full Stack" data-type="Full-Time" data-price="85">
                                    <div class="job-header">
                                        <h3 class="job-title">Full Stack JavaScript Developer</h3>
                                    </div>
                                    <div class="job-body">
                                        <p class="job-description">Build modern web applications using Node.js, React, and MongoDB. Experience with microservices architecture is a plus.</p>
                                        <div class="job-details">
                                            <div class="job-detail">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Austin, USA</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-briefcase"></i>
                                                <span>Long-term</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-user-graduate"></i>
                                                <span>Senior</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-suitcase"></i>
                                                <span>Full-Time</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-clock"></i>
                                                <span>Monthly</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-dollar-sign"></i>
                                                <span>$85/hr</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-footer">
                                        <button class="view-btn">View</button>
                                    </div>
                                </div>

                                <!-- Job Card 8 -->
                                <div class="job-card" data-location="San Francisco, USA" data-category="Mobile Development" data-type="Contract" data-price="75">
                                    <div class="job-header">
                                        <h3 class="job-title">Mobile App Developer (iOS)</h3>
                                    </div>
                                    <div class="job-body">
                                        <p class="job-description">Develop native iOS applications for a health tech startup. Swift and SwiftUI experience required.</p>
                                        <div class="job-details">
                                            <div class="job-detail">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>San Francisco, USA</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-briefcase"></i>
                                                <span>1 Year</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-user-graduate"></i>
                                                <span>Mid-Level</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-suitcase"></i>
                                                <span>Contract</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-clock"></i>
                                                <span>Monthly</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-dollar-sign"></i>
                                                <span>$75/hr</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-footer">
                                        <button class="view-btn">View</button>
                                    </div>
                                </div>

                                <!-- Job Card 9 -->
                                <div class="job-card" data-location="Remote" data-category="UI/UX" data-type="Freelance" data-price="60">
                                    <div class="job-header">
                                        <h3 class="job-title">UX/UI Designer</h3>
                                    </div>
                                    <div class="job-body">
                                        <p class="job-description">Create intuitive user experiences for web and mobile applications. Figma and Adobe XD proficiency required.</p>
                                        <div class="job-details">
                                            <div class="job-detail">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Remote</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-briefcase"></i>
                                                <span>6 Months</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-user-graduate"></i>
                                                <span>Senior</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-suitcase"></i>
                                                <span>Freelance</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-clock"></i>
                                                <span>Project-based</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-dollar-sign"></i>
                                                <span>$60/hr</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-footer">
                                        <button class="view-btn">View</button>
                                    </div>
                                </div>

                                <!-- Job Card 10 -->
                                <div class="job-card" data-location="Seattle, USA" data-category="DevOps" data-type="Full-Time" data-price="90">
                                    <div class="job-header">
                                        <h3 class="job-title">DevOps Engineer</h3>
                                    </div>
                                    <div class="job-body">
                                        <p class="job-description">Implement CI/CD pipelines and manage cloud infrastructure. Experience with AWS, Docker, and Kubernetes required.</p>
                                        <div class="job-details">
                                            <div class="job-detail">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Seattle, USA</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-briefcase"></i>
                                                <span>Permanent</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-user-graduate"></i>
                                                <span>Senior</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-suitcase"></i>
                                                <span>Full-Time</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-clock"></i>
                                                <span>Monthly</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-dollar-sign"></i>
                                                <span>$90/hr</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-footer">
                                        <button class="view-btn">View</button>
                                    </div>
                                </div>

                                <!-- Job Card 11 -->
                                <div class="job-card" data-location="Boston, USA" data-category="Data Science" data-type="Full-Time" data-price="80">
                                    <div class="job-header">
                                        <h3 class="job-title">Data Scientist</h3>
                                    </div>
                                    <div class="job-body">
                                        <p class="job-description">Analyze large datasets and build machine learning models. Python, TensorFlow, and SQL expertise required.</p>
                                        <div class="job-details">
                                            <div class="job-detail">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Boston, USA</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-briefcase"></i>
                                                <span>Long-term</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-user-graduate"></i>
                                                <span>Mid-Level</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-suitcase"></i>
                                                <span>Full-Time</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-clock"></i>
                                                <span>Monthly</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-dollar-sign"></i>
                                                <span>$80/hr</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-footer">
                                        <button class="view-btn">View</button>
                                    </div>
                                </div>

                                <!-- Job Card 12 -->
                                <div class="job-card" data-location="Remote" data-category="Blockchain" data-type="Contract" data-price="95">
                                    <div class="job-header">
                                        <h3 class="job-title">Blockchain Developer</h3>
                                    </div>
                                    <div class="job-body">
                                        <p class="job-description">Develop smart contracts and decentralized applications. Solidity and Ethereum experience required.</p>
                                        <div class="job-details">
                                            <div class="job-detail">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Remote</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-briefcase"></i>
                                                <span>6 Months</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-user-graduate"></i>
                                                <span>Senior</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-suitcase"></i>
                                                <span>Contract</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-clock"></i>
                                                <span>Monthly</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-dollar-sign"></i>
                                                <span>$95/hr</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-footer">
                                        <button class="view-btn">View</button>
                                    </div>
                                </div>
                            </div>

                            <!-- Page 3 -->
                            <div class="page" data-page="3" style="display: none;">
                                <!-- Job Card 13 -->
                                <div class="job-card" data-location="Remote" data-category="Technical Writing" data-type="Freelance" data-price="45">
                                    <div class="job-header">
                                        <h3 class="job-title">Technical Writer</h3>
                                    </div>
                                    <div class="job-body">
                                        <p class="job-description">Create documentation for APIs and software products. Experience with technical writing tools and methodologies required.</p>
                                        <div class="job-details">
                                            <div class="job-detail">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Remote</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-briefcase"></i>
                                                <span>3 Months</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-user-graduate"></i>
                                                <span>Mid-Level</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-suitcase"></i>
                                                <span>Freelance</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-clock"></i>
                                                <span>Hourly</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-dollar-sign"></i>
                                                <span>$45/hr</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-footer">
                                        <button class="view-btn">View</button>
                                    </div>
                                </div>

                                <!-- Job Card 14 -->
                                <div class="job-card" data-location="Chicago, USA" data-category="QA" data-type="Full-Time" data-price="65">
                                    <div class="job-header">
                                        <h3 class="job-title">QA Automation Engineer</h3>
                                    </div>
                                    <div class="job-body">
                                        <p class="job-description">Develop and maintain automated test suites. Experience with Selenium, Cypress, or similar tools required.</p>
                                        <div class="job-details">
                                            <div class="job-detail">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Chicago, USA</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-briefcase"></i>
                                                <span>Permanent</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-user-graduate"></i>
                                                <span>Mid-Level</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-suitcase"></i>
                                                <span>Full-Time</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-clock"></i>
                                                <span>Monthly</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-dollar-sign"></i>
                                                <span>$65/hr</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-footer">
                                        <button class="view-btn">View</button>
                                    </div>
                                </div>

                                <!-- Job Card 15 -->
                                <div class="job-card" data-location="New York, USA" data-category="Product Management" data-type="Full-Time" data-price="90">
                                    <div class="job-header">
                                        <h3 class="job-title">Product Manager</h3>
                                    </div>
                                    <div class="job-body">
                                        <p class="job-description">Lead product development from conception to launch. Experience with agile methodologies and product roadmapping required.</p>
                                        <div class="job-details">
                                            <div class="job-detail">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>New York, USA</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-briefcase"></i>
                                                <span>Long-term</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-user-graduate"></i>
                                                <span>Senior</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-suitcase"></i>
                                                <span>Full-Time</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-clock"></i>
                                                <span>Monthly</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-dollar-sign"></i>
                                                <span>$90/hr</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-footer">
                                        <button class="view-btn">View</button>
                                    </div>
                                </div>

                                <!-- Job Card 16 -->
                                <div class="job-card" data-location="Remote" data-category="Cloud Computing" data-type="Contract" data-price="100">
                                    <div class="job-header">
                                        <h3 class="job-title">Cloud Architect</h3>
                                    </div>
                                    <div class="job-body">
                                        <p class="job-description">Design and implement cloud-based solutions. Experience with multi-cloud environments and serverless architecture required.</p>
                                        <div class="job-details">
                                            <div class="job-detail">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Remote</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-briefcase"></i>
                                                <span>1 Year</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-user-graduate"></i>
                                                <span>Senior</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-suitcase"></i>
                                                <span>Contract</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-clock"></i>
                                                <span>Monthly</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-dollar-sign"></i>
                                                <span>$100/hr</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-footer">
                                        <button class="view-btn">View</button>
                                    </div>
                                </div>

                                <!-- Job Card 17 -->
                                <div class="job-card" data-location="Washington DC, USA" data-category="Cybersecurity" data-type="Full-Time" data-price="95">
                                    <div class="job-header">
                                        <h3 class="job-title">Security Engineer</h3>
                                    </div>
                                    <div class="job-body">
                                        <p class="job-description">Implement security measures and conduct vulnerability assessments. Experience with penetration testing and security frameworks required.</p>
                                        <div class="job-details">
                                            <div class="job-detail">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Washington DC, USA</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-briefcase"></i>
                                                <span>Permanent</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-user-graduate"></i>
                                                <span>Senior</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-suitcase"></i>
                                                <span>Full-Time</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-clock"></i>
                                                <span>Monthly</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-dollar-sign"></i>
                                                <span>$95/hr</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-footer">
                                        <button class="view-btn">View</button>
                                    </div>
                                </div>

                                <!-- Job Card 18 -->
                                <div class="job-card" data-location="Los Angeles, USA" data-category="Game Development" data-type="Contract" data-price="70">
                                    <div class="job-header">
                                        <h3 class="job-title">Game Developer</h3>
                                    </div>
                                    <div class="job-body">
                                        <p class="job-description">Develop interactive games for web and mobile platforms. Experience with Unity or Unreal Engine required.</p>
                                        <div class="job-details">
                                            <div class="job-detail">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>Los Angeles, USA</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-briefcase"></i>
                                                <span>6 Months</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-user-graduate"></i>
                                                <span>Mid-Level</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-suitcase"></i>
                                                <span>Contract</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-clock"></i>
                                                <span>Monthly</span>
                                            </div>
                                            <div class="job-detail">
                                                <i class="fas fa-dollar-sign"></i>
                                                <span>$70/hr</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-footer">
                                        <button class="view-btn">View</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pagination -->
                        <div class="pagination" id="pagination">
                            <div class="page-item page-arrow" onclick="changePage('prev')">
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <div class="page-item active" data-page="1">1</div>
                            <div class="page-item" data-page="2">2</div>
                            <div class="page-item" data-page="3">3</div>
                            <div class="page-item page-arrow" onclick="changePage('next')">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- My Applications Content -->
            <div id="myApplicationsContent" class="content-section">
                <div class="jobs-header">
                    <h2 class="jobs-title">Jobs you have applied</h2>
                    <div class="jobs-filters">
                        <select class="filter-select" id="statusFilter">
                            <option value="all">All Statuses</option>
                            <option value="pending">Pending</option>
                            <option value="accepted">Accepted</option>
                            <option value="rejected">Rejected</option>
                            <option value="in-review">In Review</option>
                        </select>
                        <input type="text" class="filter-input" placeholder="Search gig title" id="searchInput">
                    </div>
                </div>

                <div class="jobs-grid" id="jobsGrid">
                    <!-- Job Card 1 -->
                    <div class="job-card" data-title="Senior Marketing Specialist" data-status="pending">
                        <div class="job-header">
                            <h3 class="job-title">Senior Marketing Specialist</h3>
                            <div class="job-actions">
                                <button class="save-btn" onclick="toggleSave(this)">
                                    <i class="far fa-bookmark"></i>
                                    <span class="save-btn-text">Save</span>
                                </button>
                                <span class="status-badge status-pending">Pending</span>
                            </div>
                        </div>
                        <div class="job-body">
                            <p class="job-description">Looking for an experienced marketing professional to help with our product launch campaign...</p>
                            <div class="job-details">
                                <div class="job-detail">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Remote</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-briefcase"></i>
                                    <span>Marketing</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>Contract</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-file-alt"></i>
                                    <span>Professional</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-dollar-sign"></i>
                                    <span>$45-60</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-clock"></i>
                                    <span>Project-based</span>
                                </div>
                            </div>
                        </div>
                        <div class="job-footer">
                            <button class="view-btn">View Details</button>
                        </div>
                    </div>

                    <!-- Job Card 2 -->
                    <div class="job-card" data-title="Content Marketing Strategist" data-status="pending">
                        <div class="job-header">
                            <h3 class="job-title">Content Marketing Strategist</h3>
                            <div class="job-actions">
                                <button class="save-btn" onclick="toggleSave(this)">
                                    <i class="far fa-bookmark"></i>
                                    <span class="save-btn-text">Save</span>
                                </button>
                                <span class="status-badge status-pending">Pending</span>
                            </div>
                        </div>
                        <div class="job-body">
                            <p class="job-description">Develop and implement content marketing strategies to increase brand awareness and engagement...</p>
                            <div class="job-details">
                                <div class="job-detail">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Remote</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-briefcase"></i>
                                    <span>Content</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>Part-time</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-file-alt"></i>
                                    <span>Expert</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-dollar-sign"></i>
                                    <span>$40-55</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-clock"></i>
                                    <span>Hourly</span>
                                </div>
                            </div>
                        </div>
                        <div class="job-footer">
                            <button class="view-btn">View Details</button>
                        </div>
                    </div>

                    <!-- Job Card 3 -->
                    <div class="job-card" data-title="Social Media Manager" data-status="in-review">
                        <div class="job-header">
                            <h3 class="job-title">Social Media Manager</h3>
                            <div class="job-actions">
                                <button class="save-btn" onclick="toggleSave(this)">
                                    <i class="far fa-bookmark"></i>
                                    <span class="save-btn-text">Save</span>
                                </button>
                                <span class="status-badge status-review">In Review</span>
                            </div>
                        </div>
                        <div class="job-body">
                            <p class="job-description">Manage social media accounts and create engaging content for multiple platforms...</p>
                            <div class="job-details">
                                <div class="job-detail">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>New York, USA</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-briefcase"></i>
                                    <span>Social Media</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>Full-time</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-file-alt"></i>
                                    <span>Mid-Level</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-dollar-sign"></i>
                                    <span>$35-50</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-clock"></i>
                                    <span>Monthly</span>
                                </div>
                            </div>
                        </div>
                        <div class="job-footer">
                            <button class="view-btn">View Details</button>
                        </div>
                    </div>

                    <!-- Job Card 4 -->
                    <div class="job-card" data-title="SEO Specialist" data-status="accepted">
                        <div class="job-header">
                            <h3 class="job-title">SEO Specialist</h3>
                            <div class="job-actions">
                                <button class="save-btn saved" onclick="toggleSave(this)">
                                    <i class="fas fa-bookmark"></i>
                                    <span class="save-btn-text">Saved</span>
                                </button>
                                <span class="status-badge status-accepted">Accepted</span>
                            </div>
                        </div>
                        <div class="job-body">
                            <p class="job-description">Optimize website content and structure to improve search engine rankings...</p>
                            <div class="job-details">
                                <div class="job-detail">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Remote</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-briefcase"></i>
                                    <span>SEO</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>Contract</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-file-alt"></i>
                                    <span>Expert</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-dollar-sign"></i>
                                    <span>$50-65</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-clock"></i>
                                    <span>Project-based</span>
                                </div>
                            </div>
                        </div>
                        <div class="job-footer">
                            <button class="view-btn">View Details</button>
                        </div>
                    </div>

                    <!-- Job Card 5 -->
                    <div class="job-card" data-title="Email Marketing Coordinator" data-status="rejected">
                        <div class="job-header">
                            <h3 class="job-title">Email Marketing Coordinator</h3>
                            <div class="job-actions">
                                <button class="save-btn" onclick="toggleSave(this)">
                                    <i class="far fa-bookmark"></i>
                                    <span class="save-btn-text">Save</span>
                                </button>
                                <span class="status-badge status-rejected">Rejected</span>
                            </div>
                        </div>
                        <div class="job-body">
                            <p class="job-description">Create and manage email marketing campaigns to drive customer engagement and conversions...</p>
                            <div class="job-details">
                                <div class="job-detail">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Chicago, USA</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-briefcase"></i>
                                    <span>Email Marketing</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>Part-time</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-file-alt"></i>
                                    <span>Mid-Level</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-dollar-sign"></i>
                                    <span>$30-45</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-clock"></i>
                                    <span>Hourly</span>
                                </div>
                            </div>
                        </div>
                        <div class="job-footer">
                            <button class="view-btn">View Details</button>
                        </div>
                    </div>

                    <!-- Job Card 6 -->
                    <div class="job-card" data-title="Brand Strategist" data-status="pending">
                        <div class="job-header">
                            <h3 class="job-title">Brand Strategist</h3>
                            <div class="job-actions">
                                <button class="save-btn" onclick="toggleSave(this)">
                                    <i class="far fa-bookmark"></i>
                                    <span class="save-btn-text">Save</span>
                                </button>
                                <span class="status-badge status-pending">Pending</span>
                            </div>
                        </div>
                        <div class="job-body">
                            <p class="job-description">Develop comprehensive brand strategies and positioning for growing companies...</p>
                            <div class="job-details">
                                <div class="job-detail">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Remote</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-briefcase"></i>
                                    <span>Branding</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>Contract</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-file-alt"></i>
                                    <span>Senior</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-dollar-sign"></i>
                                    <span>$60-80</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-clock"></i>
                                    <span>Project-based</span>
                                </div>
                            </div>
                        </div>
                        <div class="job-footer">
                            <button class="view-btn">View Details</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Job Offers Content -->
            <div id="jobOffersContent" class="content-section">
                <div class="jobs-header">
                    <h2 class="jobs-title">Your Job Offers</h2>
                    <div class="jobs-filters">
                        <select class="filter-select" id="offersStatusFilter">
                            <option value="all">All Statuses</option>
                            <option value="new">New</option>
                            <option value="viewed">Viewed</option>
                            <option value="accepted">Accepted</option>
                            <option value="declined">Declined</option>
                        </select>
                        <input type="text" class="filter-input" placeholder="Search gig title" id="offersSearchInput">
                    </div>
                </div>

                <div class="jobs-grid" id="offersGrid">
                    <!-- Job Card 1 -->
                    <div class="job-card" data-title="Senior Marketing Specialist" data-status="new">
                        <div class="job-header">
                            <h3 class="job-title">Senior Marketing Specialist</h3>
                        </div>
                        <button class="add-to-cart" onclick="toggleCart(this)">
                            <i class="fas fa-plus"></i>
                            <span>Add to Cart</span>
                        </button>
                        <div class="job-body">
                            <p class="job-description">Looking for an experienced marketing professional to help with our product launch campaign and develop comprehensive marketing strategies...</p>
                            <div class="job-details">
                                <div class="job-detail">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Remote</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-briefcase"></i>
                                    <span>Marketing</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>Contract</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-file-alt"></i>
                                    <span>Professional</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-dollar-sign"></i>
                                    <span>$45-60</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-clock"></i>
                                    <span>Project-based</span>
                                </div>
                            </div>
                        </div>
                        <div class="job-footer">
                            <button class="view-btn">View Details</button>
                        </div>
                    </div>

                    <!-- Job Card 2 -->
                    <div class="job-card" data-title="Content Marketing Strategist" data-status="new">
                        <div class="job-header">
                            <h3 class="job-title">Content Marketing Strategist</h3>
                        </div>
                        <button class="add-to-cart" onclick="toggleCart(this)">
                            <i class="fas fa-plus"></i>
                            <span>Add to Cart</span>
                        </button>
                        <div class="job-body">
                            <p class="job-description">Develop and implement content marketing strategies to increase brand awareness and engagement across multiple channels...</p>
                            <div class="job-details">
                                <div class="job-detail">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Remote</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-briefcase"></i>
                                    <span>Content</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>Part-time</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-file-alt"></i>
                                    <span>Expert</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-dollar-sign"></i>
                                    <span>$40-55</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-clock"></i>
                                    <span>Hourly</span>
                                </div>
                            </div>
                        </div>
                        <div class="job-footer">
                            <button class="view-btn">View Details</button>
                        </div>
                    </div>

                    <!-- Job Card 3 -->
                    <div class="job-card" data-title="Digital Marketing Manager" data-status="viewed">
                        <div class="job-header">
                            <h3 class="job-title">Digital Marketing Manager</h3>
                        </div>
                        <button class="add-to-cart" onclick="toggleCart(this)">
                            <i class="fas fa-plus"></i>
                            <span>Add to Cart</span>
                        </button>
                        <div class="job-body">
                            <p class="job-description">Lead digital marketing initiatives including PPC, SEO, and social media campaigns...</p>
                            <div class="job-details">
                                <div class="job-detail">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>San Francisco, USA</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-briefcase"></i>
                                    <span>Digital Marketing</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>Full-time</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-file-alt"></i>
                                    <span>Senior</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-dollar-sign"></i>
                                    <span>$70-90</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-clock"></i>
                                    <span>Monthly</span>
                                </div>
                            </div>
                        </div>
                        <div class="job-footer">
                            <button class="view-btn">View Details</button>
                        </div>
                    </div>

                    <!-- Job Card 4 -->
                    <div class="job-card" data-title="Marketing Analytics Specialist" data-status="new">
                        <div class="job-header">
                            <h3 class="job-title">Marketing Analytics Specialist</h3>
                        </div>
                        <button class="add-to-cart" onclick="toggleCart(this)">
                            <i class="fas fa-plus"></i>
                            <span>Add to Cart</span>
                        </button>
                        <div class="job-body">
                            <p class="job-description">Analyze marketing data to provide insights and recommendations for campaign optimization...</p>
                            <div class="job-details">
                                <div class="job-detail">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Remote</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-briefcase"></i>
                                    <span>Analytics</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>Contract</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-file-alt"></i>
                                    <span>Mid-Level</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-dollar-sign"></i>
                                    <span>$50-65</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-clock"></i>
                                    <span>Hourly</span>
                                </div>
                            </div>
                        </div>
                        <div class="job-footer">
                            <button class="view-btn">View Details</button>
                        </div>
                    </div>

                    <!-- Job Card 5 -->
                    <div class="job-card" data-title="Growth Marketing Specialist" data-status="new">
                        <div class="job-header">
                            <h3 class="job-title">Growth Marketing Specialist</h3>
                        </div>
                        <button class="add-to-cart" onclick="toggleCart(this)">
                            <i class="fas fa-plus"></i>
                            <span>Add to Cart</span>
                        </button>
                        <div class="job-body">
                            <p class="job-description">Develop and implement growth strategies to increase user acquisition and retention...</p>
                            <div class="job-details">
                                <div class="job-detail">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>New York, USA</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-briefcase"></i>
                                    <span>Growth</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>Full-time</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-file-alt"></i>
                                    <span>Senior</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-dollar-sign"></i>
                                    <span>$65-85</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-clock"></i>
                                    <span>Monthly</span>
                                </div>
                            </div>
                        </div>
                        <div class="job-footer">
                            <button class="view-btn">View Details</button>
                        </div>
                    </div>

                    <!-- Job Card 6 -->
                    <div class="job-card" data-title="Product Marketing Manager" data-status="new">
                        <div class="job-header">
                            <h3 class="job-title">Product Marketing Manager</h3>
                        </div>
                        <button class="add-to-cart" onclick="toggleCart(this)">
                            <i class="fas fa-plus"></i>
                            <span>Add to Cart</span>
                        </button>
                        <div class="job-body">
                            <p class="job-description">Create and execute marketing strategies for new product launches and feature releases...</p>
                            <div class="job-details">
                                <div class="job-detail">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>Remote</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-briefcase"></i>
                                    <span>Product Marketing</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>Contract</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-file-alt"></i>
                                    <span>Senior</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-dollar-sign"></i>
                                    <span>$60-80</span>
                                </div>
                                <div class="job-detail">
                                    <i class="fas fa-clock"></i>
                                    <span>Project-based</span>
                                </div>
                            </div>
                        </div>
                        <div class="job-footer">
                            <button class="view-btn">View Details</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- My Clients Content -->
            <div id="myClientsContent" class="content-section">
                <div class="clients-header">
                    <h2 class="clients-title">My Clients</h2>
                    <div class="clients-search">
                        <input type="text" class="search-input" placeholder="Search client name" id="clientSearchInput">
                    </div>
                </div>

                <div class="clients-scroll">
                    <div class="clients-container" id="clientsContainer">
                        <!-- Client Card 1 -->
                        <div class="client-card" data-name="Tech Innovations Inc">
                            <div class="client-header">
                                <h3 class="client-title">Tech Innovations Inc</h3>
                            </div>
                            <button class="favorite-btn" onclick="toggleFavorite(this)">
                                <i class="far fa-star"></i>
                                <span>Favorite</span>
                            </button>
                            <div class="client-body">
                                <p class="client-description">A leading technology company specializing in innovative software solutions for businesses of all sizes. Currently working on their product launch campaign.</p>
                                <div class="client-details">
                                    <div class="client-detail">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <span>San Francisco, CA</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-briefcase"></i>
                                        <span>Technology</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-chart-bar"></i>
                                        <span>Enterprise</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-file-alt"></i>
                                        <span>Long-term</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-dollar-sign"></i>
                                        <span>$60/hr</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-clock"></i>
                                        <span>20 hrs/week</span>
                                    </div>
                                </div>
                            </div>
                            <div class="client-footer">
                                <button class="view-btn">View Details</button>
                            </div>
                        </div>

                        <!-- Client Card 2 -->
                        <div class="client-card" data-name="Global Marketing Solutions">
                            <div class="client-header">
                                <h3 class="client-title">Global Marketing Solutions</h3>
                            </div>
                            <button class="favorite-btn" onclick="toggleFavorite(this)">
                                <i class="far fa-star"></i>
                                <span>Favorite</span>
                            </button>
                            <div class="client-body">
                                <p class="client-description">An international marketing agency that helps businesses expand their global reach through strategic marketing campaigns and localization services.</p>
                                <div class="client-details">
                                    <div class="client-detail">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <span>London, UK</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-briefcase"></i>
                                        <span>Marketing</span>
                                    </div>
                                <div class="client-detail">
                                        <i class="fas fa-chart-bar"></i>
                                        <span>Agency</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-file-alt"></i>
                                        <span>Project-based</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-dollar-sign"></i>
                                        <span>$55/hr</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-clock"></i>
                                        <span>15 hrs/week</span>
                                    </div>
                                </div>
                            </div>
                            <div class="client-footer">
                                <button class="view-btn">View Details</button>
                            </div>
                        </div>

                        <!-- Client Card 3 -->
                        <div class="client-card" data-name="Digital Transformation Co">
                            <div class="client-header">
                                <h3 class="client-title">Digital Transformation Co</h3>
                            </div>
                            <button class="favorite-btn" onclick="toggleFavorite(this)">
                                <i class="far fa-star"></i>
                                <span>Favorite</span>
                            </button>
                            <div class="client-body">
                                <p class="client-description">A consultancy specializing in helping traditional businesses adopt digital technologies and processes.</p>
                                <div class="client-details">
                                    <div class="client-detail">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <span>New York, USA</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-briefcase"></i>
                                        <span>Consulting</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-chart-bar"></i>
                                        <span>Mid-size</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-file-alt"></i>
                                        <span>Long-term</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-dollar-sign"></i>
                                        <span>$65/hr</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-clock"></i>
                                        <span>25 hrs/week</span>
                                    </div>
                                </div>
                            </div>
                            <div class="client-footer">
                                <button class="view-btn">View Details</button>
                            </div>
                        </div>

                        <!-- Client Card 4 -->
                        <div class="client-card" data-name="E-commerce Ventures">
                            <div class="client-header">
                                <h3 class="client-title">E-commerce Ventures</h3>
                            </div>
                            <button class="favorite-btn" onclick="toggleFavorite(this)">
                                <i class="far fa-star"></i>
                                <span>Favorite</span>
                            </button>
                            <div class="client-body">
                                <p class="client-description">A fast-growing e-commerce platform looking to expand their market presence through strategic marketing initiatives.</p>
                                <div class="client-details">
                                    <div class="client-detail">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <span>Toronto, Canada</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-briefcase"></i>
                                        <span>E-commerce</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-chart-bar"></i>
                                        <span>Startup</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-file-alt"></i>
                                        <span>6 Months</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-dollar-sign"></i>
                                        <span>$50/hr</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-clock"></i>
                                        <span>20 hrs/week</span>
                                    </div>
                                </div>
                            </div>
                            <div class="client-footer">
                                <button class="view-btn">View Details</button>
                            </div>
                        </div>

                        <!-- Client Card 5 -->
                        <div class="client-card" data-name="HealthTech Solutions">
                            <div class="client-header">
                                <h3 class="client-title">HealthTech Solutions</h3>
                            </div>
                            <button class="favorite-btn" onclick="toggleFavorite(this)">
                                <i class="far fa-star"></i>
                                <span>Favorite</span>
                            </button>
                            <div class="client-body">
                                <p class="client-description">A healthcare technology company developing innovative solutions for patient care and hospital management.</p>
                                <div class="client-details">
                                    <div class="client-detail">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <span>Boston, USA</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-briefcase"></i>
                                        <span>Healthcare</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-chart-bar"></i>
                                        <span>Mid-size</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-file-alt"></i>
                                        <span>Long-term</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-dollar-sign"></i>
                                        <span>$70/hr</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-clock"></i>
                                        <span>30 hrs/week</span>
                                    </div>
                                </div>
                            </div>
                            <div class="client-footer">
                                <button class="view-btn">View Details</button>
                            </div>
                        </div>

                        <!-- Client Card 6 -->
                        <div class="client-card" data-name="Sustainable Energy Group">
                            <div class="client-header">
                                <h3 class="client-title">Sustainable Energy Group</h3>
                            </div>
                            <button class="favorite-btn" onclick="toggleFavorite(this)">
                                <i class="far fa-star"></i>
                                <span>Favorite</span>
                            </button>
                            <div class="client-body">
                                <p class="client-description">A renewable energy company focused on developing and marketing sustainable energy solutions.</p>
                                <div class="client-details">
                                    <div class="client-detail">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <span>Berlin, Germany</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-briefcase"></i>
                                        <span>Energy</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-chart-bar"></i>
                                        <span>Enterprise</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-file-alt"></i>
                                        <span>Project-based</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-dollar-sign"></i>
                                        <span>$65/hr</span>
                                    </div>
                                    <div class="client-detail">
                                        <i class="fas fa-clock"></i>
                                        <span>25 hrs/week</span>
                                    </div>
                                </div>
                            </div>
                            <div class="client-footer">
                                <button class="view-btn">View Details</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Profile Visibility Modal -->
    <div id="visibilityModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Edit Profile Visibility</h2>
                <span class="close-modal" id="closeModal">&times;</span>
            </div>
            <p class="modal-description">
                Who do you want to see your profile? Simply select an option to control your visibility
                and searchability. Market your profile when and where you want.
            </p>

            <form id="visibilityForm">
                <label class="radio-option">
                    <input type="radio" name="visibility" value="public" class="radio-input" checked>
                    <span class="radio-label">Public</span>
                    <p class="info">Your profile is visible to the general public and will show up in search engine results.</p>
                </label>

                <label class="radio-option">
                    <input type="radio" name="visibility" value="genius-users" class="radio-input">
                    <span class="radio-label">Genius Users Only</span>
                    <p class="info">Only logged-in Genius users will see your profile.</p>
                </label>

                <label class="radio-option">
                    <input type="radio" name="visibility" value="private" class="radio-input">
                    <span class="radio-label">Private</span>
                    <p class="info">Your profile won't appear in any search results, not even on Genius. To view your profile, users must have a direct link and be logged in.</p>
                </label>

                <div class="buttons">
                    <button type="button" class="cancel" id="cancelVisibility">Cancel</button>
                    <button type="submit" class="save">Save</button>
                </div>
            </form>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Mobile Menu Toggle
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const mobileMenu = document.getElementById('mobileMenu');

        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.toggle('active');
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', (event) => {
            if (!mobileMenuBtn.contains(event.target) && !mobileMenu.contains(event.target)) {
                mobileMenu.classList.remove('active');
            }
        });

        // Toggle Filter Sections
        const filterSections = [
            { header: 'promoteHeader', content: 'promoteContent' },
            { header: 'preferencesHeader', content: 'preferencesContent' },
            { header: 'proposalHeader', content: 'proposalContent' }
        ];

        filterSections.forEach(section => {
            const headerEl = document.getElementById(section.header);
            const contentEl = document.getElementById(section.content);

            headerEl.addEventListener('click', () => {
                headerEl.classList.toggle('active');
                contentEl.classList.toggle('show');
            });
        });

        // Toggle Switch
        const availabilityToggle = document.getElementById('availabilityToggle');
        const toggleStatus = document.getElementById('toggleStatus');

        availabilityToggle.addEventListener('click', () => {
            availabilityToggle.classList.toggle('on');
            toggleStatus.textContent = availabilityToggle.classList.contains('on') ? 'ON' : 'OFF';
        });

        // Quote Slider
        const quotes = [
            // Format: [top text, bottom text, isTopBigger (true/false)]
            ["Getting a job shouldn't be expensive.", "No connect or bids needed.", true],
            ["No one should pay", "TO PROVE THEIR WORTH.", false],
            ["OPPORTUNITIES SHOULD BE OPEN,", "not auctioned.", true],
            ["Your skills should speak louder", "THAN YOUR CONNECTIONS.", false],
            ["HARD WORK BEATS PRIVILEGE", "when given a fair shot.", true],
            ["Merit should be", "THE ONLY CURRENCY IN THE JOB MARKET.", false],
            ["THE RIGHT JOB FINDS THE RIGHT TALENT,", "not the highest bidder.", true],
            ["If you have to pay to work,", "IT'S NOT A JOB—IT'S A SCAM.", false],
            ["THE BEST JOBS GO TO THE BEST PEOPLE—", "not the best payers.", true],
            ["Hiring should be based on talent,", "NOT TRANSACTIONS.", false]
        ];

        let currentQuoteIndex = 0;

        function changeQuote(direction) {
            currentQuoteIndex = (currentQuoteIndex + direction + quotes.length) % quotes.length;
            const [mainText, subText, isTopBigger] = quotes[currentQuoteIndex];

            const mainTextElement = document.querySelector('.main-text');
            const subTextElement = document.querySelector('.sub-text');

            if (isTopBigger) {
                mainTextElement.style.fontSize = '2.5rem';
                subTextElement.style.fontSize = '1.8rem';
            } else {
                mainTextElement.style.fontSize = '1.8rem';
                subTextElement.style.fontSize = '2.5rem';
            }

            mainTextElement.textContent = mainText;
            subTextElement.textContent = subText;
        }

        // Initialize first quote
        changeQuote(0);

        // Automatically change quote every 5 seconds
        setInterval(() => changeQuote(1), 5000);

        // Filter Jobs
        const jobSearch = document.getElementById('jobSearch');
        const locationFilter = document.getElementById('locationFilter');
        const categoryFilter = document.getElementById('categoryFilter');
        const typeFilter = document.getElementById('typeFilter');
        const priceFilter = document.getElementById('priceFilter');
        const clearFiltersBtn = document.getElementById('clearFilters');
        const jobsList = document.getElementById('jobsList');
        const noResults = document.getElementById('noResults');
        const jobCards = document.querySelectorAll('.job-card');

        function filterJobs() {
            const searchTerm = jobSearch.value.toLowerCase();
            const location = locationFilter.value.toLowerCase();
            const category = categoryFilter.value.toLowerCase();
            const type = typeFilter.value.toLowerCase();
            const price = parseFloat(priceFilter.value) || 0;

            let visibleCount = 0;

            jobCards.forEach(card => {
                const cardLocation = card.getAttribute('data-location')?.toLowerCase() || '';
                const cardCategory = card.getAttribute('data-category')?.toLowerCase() || '';
                const cardType = card.getAttribute('data-type')?.toLowerCase() || '';
                const cardPrice = parseFloat(card.getAttribute('data-price')) || 0;

                const cardTitle = card.querySelector('.job-title')?.textContent.toLowerCase() || '';
                const cardDescription = card.querySelector('.job-description')?.textContent.toLowerCase() || '';

                const matchesSearch = !searchTerm || cardTitle.includes(searchTerm) || cardDescription.includes(searchTerm);
                const matchesLocation = !location || cardLocation.includes(location);
                const matchesCategory = !category || cardCategory.includes(category);
                const matchesType = !type || cardType.includes(type);
                const matchesPrice = !price || cardPrice >= price;

                const shouldShow = matchesSearch && matchesLocation && matchesCategory && matchesType && matchesPrice;

                card.style.display = shouldShow ? 'block' : 'none';

                if (shouldShow) {
                    visibleCount++;
                }
            });

            jobsList.style.display = visibleCount > 0 ? 'block' : 'none';
            noResults.style.display = visibleCount > 0 ? 'none' : 'block';

            // Update pagination if needed
            document.getElementById('pagination').style.display = visibleCount > 0 ? 'flex' : 'none';
        }

        jobSearch.addEventListener('input', filterJobs);
        locationFilter.addEventListener('change', filterJobs);
        categoryFilter.addEventListener('change', filterJobs);
        typeFilter.addEventListener('change', filterJobs);
        priceFilter.addEventListener('input', filterJobs);

        clearFiltersBtn.addEventListener('click', () => {
            jobSearch.value = '';
            locationFilter.selectedIndex = 0;
            categoryFilter.selectedIndex = 0;
            typeFilter.selectedIndex = 0;
            priceFilter.value = '';

            filterJobs();
        });

        // Pagination
        function changePage(direction) {
            const pageItems = document.querySelectorAll('.pagination .page-item:not(.page-arrow)');
            const activePage = document.querySelector('.pagination .page-item.active');
            const currentPageNum = parseInt(activePage.textContent);
            const pages = document.querySelectorAll('.page');

            // Hide all pages
            pages.forEach(page => page.style.display = 'none');

            if (direction === 'next' && currentPageNum < pageItems.length) {
                // Show next page
                document.querySelector(`[data-page="${currentPageNum + 1}"]`).style.display = 'block';
                activePage.classList.remove('active');
                document.querySelector(`.page-item[data-page="${currentPageNum + 1}"]`).classList.add('active');
            } else if (direction === 'prev' && currentPageNum > 1) {
                // Show previous page
                document.querySelector(`[data-page="${currentPageNum - 1}"]`).style.display = 'block';
                activePage.classList.remove('active');
                document.querySelector(`.page-item[data-page="${currentPageNum - 1}"]`).classList.add('active');
            }
        }

        // Add click handlers to page numbers
        document.querySelectorAll('.pagination .page-item:not(.page-arrow)').forEach(item => {
            item.addEventListener('click', () => {
                const pageNum = parseInt(item.textContent);
                const pages = document.querySelectorAll('.page');

                // Hide all pages
                pages.forEach(page => page.style.display = 'none');

                // Show selected page
                document.querySelector(`[data-page="${pageNum}"]`).style.display = 'block';

                // Update active state
                document.querySelector('.page-item.active').classList.remove('active');
                item.classList.add('active');
            });
        });

        // Content Switching
        const dashboardBtn = document.getElementById('dashboardBtn');
        const myApplicationsBtn = document.getElementById('myApplicationsBtn');
        const jobOffersBtn = document.getElementById('jobOffersBtn');
        const myClientsBtn = document.getElementById('myClientsBtn');

        const dashboardContent = document.getElementById('dashboardContent');
        const myApplicationsContent = document.getElementById('myApplicationsContent');
        const jobOffersContent = document.getElementById('jobOffersContent');
        const myClientsContent = document.getElementById('myClientsContent');
        const introductionSection = document.getElementById('introductionSection');
        const quoteSlider = document.getElementById('quoteSlider');

        // Function to show a specific content section
        function showContent(contentElement, buttonElement) {
            // Hide all content sections
            dashboardContent.classList.remove('active');
            myApplicationsContent.classList.remove('active');
            jobOffersContent.classList.remove('active');
            myClientsContent.classList.remove('active');

            // Remove active class from all buttons
            dashboardBtn.classList.remove('active');
            myApplicationsBtn.classList.remove('active');
            jobOffersBtn.classList.remove('active');
            myClientsBtn.classList.remove('active');

            // Show the selected content section
            contentElement.classList.add('active');

            // Add active class to the clicked button
            if (buttonElement) {
                buttonElement.classList.add('active');
            }

            // Show/hide introduction and quote slider based on section
            if (contentElement === dashboardContent) {
                introductionSection.style.display = 'block';
                quoteSlider.style.display = 'block';
            } else {
                introductionSection.style.display = 'none';
                quoteSlider.style.display = 'none';
                }
        }

        // Event listeners for buttons
        dashboardBtn.addEventListener('click', () => {
            showContent(dashboardContent, dashboardBtn);
        });

        myApplicationsBtn.addEventListener('click', () => {
            showContent(myApplicationsContent, myApplicationsBtn);
        });

        jobOffersBtn.addEventListener('click', () => {
            showContent(jobOffersContent, jobOffersBtn);
        });

        myClientsBtn.addEventListener('click', () => {
            showContent(myClientsContent, myClientsBtn);
        });

        // Toggle Save Button (My Applications)
        function toggleSave(button) {
            button.classList.toggle('saved');
            const icon = button.querySelector('i');
            const text = button.querySelector('.save-btn-text');

            if (button.classList.contains('saved')) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                text.textContent = 'Saved';
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                text.textContent = 'Save';
            }
        }

        // Toggle Add to Cart Button (Job Offers)
        function toggleCart(button) {
            button.classList.toggle('added');
            const icon = button.querySelector('i');
            const text = button.querySelector('span');

            if (button.classList.contains('added')) {
                icon.classList.remove('fa-plus');
                icon.classList.add('fa-check');
                text.textContent = 'Added to Cart';
            } else {
                icon.classList.remove('fa-check');
                icon.classList.add('fa-plus');
                text.textContent = 'Add to Cart';
            }
        }

        // Toggle Favorite Button (My Clients)
        function toggleFavorite(button) {
            button.classList.toggle('favorited');
            const icon = button.querySelector('i');
            const text = button.querySelector('span');

            if (button.classList.contains('favorited')) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                text.textContent = 'Favorited';
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                text.textContent = 'Favorite';
            }
        }

        // Profile Visibility Modal
        const visibilityModal = document.getElementById('visibilityModal');
        const editVisibilityBtn = document.getElementById('editVisibilityBtn');
        const closeModal = document.getElementById('closeModal');
        const cancelVisibility = document.getElementById('cancelVisibility');
        const visibilityForm = document.getElementById('visibilityForm');

        // Open modal
        editVisibilityBtn.addEventListener('click', (e) => {
            e.preventDefault();
            visibilityModal.style.display = 'block';
        });

        // Close modal
        closeModal.addEventListener('click', () => {
            visibilityModal.style.display = 'none';
        });

        cancelVisibility.addEventListener('click', () => {
            visibilityModal.style.display = 'none';
        });

        // Close modal when clicking outside
        window.addEventListener('click', (event) => {
            if (event.target === visibilityModal) {
                visibilityModal.style.display = 'none';
            }
        });

        // Handle form submission
        visibilityForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const selectedOption = document.querySelector('input[name="visibility"]:checked').value;
            const visibilityPill = document.querySelector('.visibility-pill');

            // Update visibility pill text based on selection
            if (selectedOption === 'public') {
                visibilityPill.textContent = 'Public';
            } else if (selectedOption === 'genius-users') {
                visibilityPill.textContent = 'Genius Users Only';
            } else if (selectedOption === 'private') {
                visibilityPill.textContent = 'Private';
            }

            visibilityModal.style.display = 'none';
        });

        // Initialize page display
        document.addEventListener('DOMContentLoaded', () => {
            // Show first page, hide others
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => {
                page.style.display = page.dataset.page === '1' ? 'block' : 'none';
            });
        });

        document.addEventListener('DOMContentLoaded', function() {
            // For mobile devices, make dropdown items clickable to expand
            const dropdownTriggers = document.querySelectorAll('.dropdown-trigger');

            dropdownTriggers.forEach(trigger => {
                trigger.addEventListener('click', function(e) {
                    if (window.innerWidth < 768) {
                        e.preventDefault();
                        const dropdown = this.nextElementSibling;
                        dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
                    }
                });
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.dropdown')) {
                    const dropdownMenus = document.querySelectorAll('.dropdown-menu');
                    dropdownMenus.forEach(menu => {
                        menu.style.display = 'none';
                    });
                }

                // Close profile dropdown when clicking outside
                if (!e.target.closest('.profile-dropdown')) {
                    const profileDropdown = document.getElementById('profileDropdown');
                    if (profileDropdown) {
                        profileDropdown.classList.remove('active');
                    }
                }

                // Close notification dropdown when clicking outside
                if (!e.target.closest('.notification-dropdown')) {
                    const notificationDropdown = document.getElementById('notificationDropdown');
                    if (notificationDropdown) {
                        notificationDropdown.classList.remove('active');
                    }
                }
            });

            // Profile dropdown toggle
            const profileIcon = document.getElementById('profileIcon');
            const profileDropdown = document.getElementById('profileDropdown');

            if (profileIcon && profileDropdown) {
                profileIcon.addEventListener('click', function(event) {
                    event.stopPropagation();
                    profileDropdown.classList.toggle('active');

                    // Close notification dropdown if open
                    const notificationDropdown = document.getElementById('notificationDropdown');
                    if (notificationDropdown && notificationDropdown.classList.contains('active')) {
                        notificationDropdown.classList.remove('active');
                    }
                });

                profileDropdown.addEventListener('click', (event) => {
                    event.stopPropagation();
                });
            }

            // Notification dropdown toggle
            const notificationBtn = document.getElementById('notificationBtn');
            const notificationDropdown = document.getElementById('notificationDropdown');
            const markAllReadBtn = document.getElementById('markAllRead');

            if (notificationBtn && notificationDropdown) {
                notificationBtn.addEventListener('click', function(event) {
                    event.stopPropagation();
                    notificationDropdown.classList.toggle('active');

                    // Close profile dropdown if open
                    if (profileDropdown && profileDropdown.classList.contains('active')) {
                        profileDropdown.classList.remove('active');
                    }
                });

                notificationDropdown.addEventListener('click', (event) => {
                    event.stopPropagation();
                });

                // Mark all notifications as read
                if (markAllReadBtn) {
                    markAllReadBtn.addEventListener('click', function() {
                        const unreadNotifications = document.querySelectorAll('.notification-item.unread');
                        unreadNotifications.forEach(notification => {
                            notification.classList.remove('unread');
                        });

                        // Remove the notification indicator
                        const indicator = document.querySelector('.notification-indicator');
                        if (indicator) {
                            indicator.style.display = 'none';
                        }
                    });
                }

                // Mark individual notification as read when clicked
                const notificationItems = document.querySelectorAll('.notification-item');
                notificationItems.forEach(item => {
                    item.addEventListener('click', function() {
                        this.classList.remove('unread');

                        // Check if there are any unread notifications left
                        const unreadNotifications = document.querySelectorAll('.notification-item.unread');
                        if (unreadNotifications.length === 0) {
                            const indicator = document.querySelector('.notification-indicator');
                            if (indicator) {
                                indicator.style.display = 'none';
                            }
                        }
                    });
                });
            }

            // Add click handlers for profile action buttons to open modals
            const myApplicationsBtn = document.getElementById('myApplicationsBtn');
            const jobOffersBtn = document.getElementById('jobOffersBtn');
            const myClientsBtn = document.getElementById('myClientsBtn');

            console.log('Button elements found:', {
                myApplicationsBtn: !!myApplicationsBtn,
                jobOffersBtn: !!jobOffersBtn,
                myClientsBtn: !!myClientsBtn
            });

            if (myApplicationsBtn) {
                console.log('Adding event listener to myApplicationsBtn');
                myApplicationsBtn.addEventListener('click', function(e) {
                    console.log('myApplicationsBtn clicked!');
                    e.preventDefault();
                    openModal('myApplicationsModal');
                });
            } else {
                console.error('myApplicationsBtn not found!');
            }

            if (jobOffersBtn) {
                console.log('Adding event listener to jobOffersBtn');
                jobOffersBtn.addEventListener('click', function(e) {
                    console.log('jobOffersBtn clicked!');
                    e.preventDefault();
                    openModal('jobOffersModal');
                });
            } else {
                console.error('jobOffersBtn not found!');
            }

            if (myClientsBtn) {
                console.log('Adding event listener to myClientsBtn');
                myClientsBtn.addEventListener('click', function(e) {
                    console.log('myClientsBtn clicked!');
                    e.preventDefault();
                    openModal('myClientsModal');
                });
            } else {
                console.error('myClientsBtn not found!');
            }

            // Quote Slider Functionality
            const quotes = [
                // Format: [top text, bottom text, isTopBigger (true/false)]
                ["Getting a job shouldn't be expensive.", "No connect or bids needed.", true],
                ["No one should pay", "TO PROVE THEIR WORTH.", false],
                ["OPPORTUNITIES SHOULD BE OPEN,", "not auctioned.", true],
                ["Your skills should speak louder", "THAN YOUR CONNECTIONS.", false],
                ["HARD WORK BEATS PRIVILEGE", "when given a fair shot.", true],
                ["Merit should be", "THE ONLY CURRENCY IN THE JOB MARKET.", false],
                ["THE RIGHT JOB FINDS THE RIGHT TALENT,", "not the highest bidder.", true],
                ["If you have to pay to work,", "IT'S NOT A JOB—IT'S A SCAM.", false],
                ["THE BEST JOBS GO TO THE BEST PEOPLE—", "not the best payers.", true],
                ["Hiring should be based on talent,", "NOT TRANSACTIONS.", false]
            ];

            let currentQuoteIndex = 0;

            window.changeQuote = function(direction) {
                currentQuoteIndex = (currentQuoteIndex + direction + quotes.length) % quotes.length;
                const [mainText, subText, isTopBigger] = quotes[currentQuoteIndex];

                const mainTextElement = document.querySelector('.main-text');
                const subTextElement = document.querySelector('.sub-text');

                if (mainTextElement && subTextElement) {
                    if (isTopBigger) {
                        mainTextElement.style.fontSize = '2.5rem';
                        subTextElement.style.fontSize = '1.8rem';
                    } else {
                        mainTextElement.style.fontSize = '1.8rem';
                        subTextElement.style.fontSize = '2.5rem';
                    }

                    mainTextElement.textContent = mainText;
                    subTextElement.textContent = subText;
                }
            };

            // Initialize first quote
            changeQuote(0);

            // Automatically change quote every 5 seconds
            setInterval(() => changeQuote(1), 5000);
        });
    </script>

    <!-- My Applications Modal -->
    <div id="myApplicationsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">My Applications</h2>
                <span class="close-modal" onclick="closeModal('myApplicationsModal')">&times;</span>
            </div>
            <p class="modal-description">
                View all your job applications and their current status.
            </p>

            <div class="modal-jobs-list">
                <!-- Application 1 -->
                <div class="modal-job-card">
                    <div class="modal-job-title">Senior Marketing Specialist</div>
                    <div class="modal-job-description">Looking for an experienced marketing professional to help with our product launch campaign and develop comprehensive marketing strategies.</div>
                    <div class="modal-job-details">
                        <div class="modal-job-detail">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Remote</span>
                        </div>
                        <div class="modal-job-detail">
                            <i class="fas fa-briefcase"></i>
                            <span>Marketing</span>
                        </div>
                        <div class="modal-job-detail">
                            <i class="fas fa-clock"></i>
                            <span>Full-time</span>
                        </div>
                        <div class="modal-job-detail">
                            <i class="fas fa-dollar-sign"></i>
                            <span>$3,000 - $5,000</span>
                        </div>
                        <div class="modal-job-detail">
                            <i class="fas fa-info-circle"></i>
                            <span style="color: #c2410c;">Status: Pending</span>
                        </div>
                    </div>
                </div>

                <!-- Application 2 -->
                <div class="modal-job-card">
                    <div class="modal-job-title">Frontend Developer</div>
                    <div class="modal-job-description">Develop and maintain web applications for a leading fintech company. Strong skills in React and Redux required.</div>
                    <div class="modal-job-details">
                        <div class="modal-job-detail">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>San Francisco, CA</span>
                        </div>
                        <div class="modal-job-detail">
                            <i class="fas fa-briefcase"></i>
                            <span>Technology</span>
                        </div>
                        <div class="modal-job-detail">
                            <i class="fas fa-clock"></i>
                            <span>Full-time</span>
                        </div>
                        <div class="modal-job-detail">
                            <i class="fas fa-dollar-sign"></i>
                            <span>$4,000 - $6,000</span>
                        </div>
                        <div class="modal-job-detail">
                            <i class="fas fa-info-circle"></i>
                            <span style="color: #047857;">Status: Accepted</span>
                        </div>
                    </div>
                </div>

                <!-- Application 3 -->
                <div class="modal-job-card">
                    <div class="modal-job-title">UX/UI Designer</div>
                    <div class="modal-job-description">Create intuitive and engaging user experiences for mobile and web applications. Portfolio review required.</div>
                    <div class="modal-job-details">
                        <div class="modal-job-detail">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>New York, NY</span>
                        </div>
                        <div class="modal-job-detail">
                            <i class="fas fa-briefcase"></i>
                            <span>Design</span>
                        </div>
                        <div class="modal-job-detail">
                            <i class="fas fa-clock"></i>
                            <span>Contract</span>
                        </div>
                        <div class="modal-job-detail">
                            <i class="fas fa-dollar-sign"></i>
                            <span>$2,500 - $4,000</span>
                        </div>
                        <div class="modal-job-detail">
                            <i class="fas fa-info-circle"></i>
                            <span style="color: #1d4ed8;">Status: In Review</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="buttons">
                <button type="button" class="cancel" onclick="closeModal('myApplicationsModal')">Close</button>
            </div>
        </div>
    </div>

    <!-- Job Offers Modal -->
    <div id="jobOffersModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Job Offers</h2>
                <span class="close-modal" onclick="closeModal('jobOffersModal')">&times;</span>
            </div>
            <p class="modal-description">
                View all job offers you've received from potential clients.
            </p>

            <div class="modal-jobs-list">
                <!-- Offer 1 -->
                <div class="modal-job-card">
                    <div class="modal-job-title">Senior Marketing Specialist</div>
                    <div class="modal-job-description">We would like to offer you this position based on your excellent portfolio and experience in digital marketing.</div>
                    <div class="modal-job-details">
                        <div class="modal-job-detail">
                            <i class="fas fa-building"></i>
                            <span>Tech Innovations Inc</span>
                        </div>
                        <div class="modal-job-detail">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Remote</span>
                        </div>
                        <div class="modal-job-detail">
                            <i class="fas fa-clock"></i>
                            <span>Full-time</span>
                        </div>
                        <div class="modal-job-detail">
                            <i class="fas fa-dollar-sign"></i>
                            <span>$5,000/month</span>
                        </div>
                        <div class="modal-job-detail">
                            <i class="fas fa-calendar"></i>
                            <span>Received: 2 days ago</span>
                        </div>
                    </div>
                </div>

                <!-- Offer 2 -->
                <div class="modal-job-card">
                    <div class="modal-job-title">Web Development Project</div>
                    <div class="modal-job-description">Exclusive offer for developing our company website. We were impressed by your previous work and would like to work with you.</div>
                    <div class="modal-job-details">
                        <div class="modal-job-detail">
                            <i class="fas fa-building"></i>
                            <span>Digital Solutions LLC</span>
                        </div>
                        <div class="modal-job-detail">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Los Angeles, CA</span>
                        </div>
                        <div class="modal-job-detail">
                            <i class="fas fa-clock"></i>
                            <span>Project-based</span>
                        </div>
                        <div class="modal-job-detail">
                            <i class="fas fa-dollar-sign"></i>
                            <span>$8,000 fixed</span>
                        </div>
                        <div class="modal-job-detail">
                            <i class="fas fa-calendar"></i>
                            <span>Received: 1 week ago</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="buttons">
                <button type="button" class="cancel" onclick="closeModal('jobOffersModal')">Close</button>
            </div>
        </div>
    </div>

    <!-- My Clients Modal -->
    <div id="myClientsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">My Clients</h2>
                <span class="close-modal" onclick="closeModal('myClientsModal')">&times;</span>
            </div>
            <p class="modal-description">
                View all your current and past clients you've worked with.
            </p>

            <div class="modal-clients-list">
                <!-- Client 1 -->
                <div class="modal-client-card">
                    <div class="modal-client-title">Tech Innovations Inc</div>
                    <div class="modal-client-description">A leading technology company specializing in innovative software solutions for businesses of all sizes. Currently working on their product launch campaign.</div>
                    <div class="modal-client-details">
                        <div class="modal-client-detail">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>San Francisco, CA</span>
                        </div>
                        <div class="modal-client-detail">
                            <i class="fas fa-briefcase"></i>
                            <span>Technology</span>
                        </div>
                        <div class="modal-client-detail">
                            <i class="fas fa-users"></i>
                            <span>50-200 employees</span>
                        </div>
                        <div class="modal-client-detail">
                            <i class="fas fa-star"></i>
                            <span>5.0 rating</span>
                        </div>
                        <div class="modal-client-detail">
                            <i class="fas fa-handshake"></i>
                            <span>Active Client</span>
                        </div>
                    </div>
                </div>

                <!-- Client 2 -->
                <div class="modal-client-card">
                    <div class="modal-client-title">Global Marketing Solutions</div>
                    <div class="modal-client-description">International marketing agency helping brands expand their reach globally. Specializes in digital marketing and brand strategy.</div>
                    <div class="modal-client-details">
                        <div class="modal-client-detail">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>New York, NY</span>
                        </div>
                        <div class="modal-client-detail">
                            <i class="fas fa-briefcase"></i>
                            <span>Marketing</span>
                        </div>
                        <div class="modal-client-detail">
                            <i class="fas fa-users"></i>
                            <span>200+ employees</span>
                        </div>
                        <div class="modal-client-detail">
                            <i class="fas fa-star"></i>
                            <span>4.8 rating</span>
                        </div>
                        <div class="modal-client-detail">
                            <i class="fas fa-handshake"></i>
                            <span>Past Client</span>
                        </div>
                    </div>
                </div>

                <!-- Client 3 -->
                <div class="modal-client-card">
                    <div class="modal-client-title">Creative Design Studio</div>
                    <div class="modal-client-description">Boutique design studio creating beautiful and functional designs for startups and established businesses.</div>
                    <div class="modal-client-details">
                        <div class="modal-client-detail">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Austin, TX</span>
                        </div>
                        <div class="modal-client-detail">
                            <i class="fas fa-briefcase"></i>
                            <span>Design</span>
                        </div>
                        <div class="modal-client-detail">
                            <i class="fas fa-users"></i>
                            <span>10-50 employees</span>
                        </div>
                        <div class="modal-client-detail">
                            <i class="fas fa-star"></i>
                            <span>4.9 rating</span>
                        </div>
                        <div class="modal-client-detail">
                            <i class="fas fa-handshake"></i>
                            <span>Active Client</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="buttons">
                <button type="button" class="cancel" onclick="closeModal('myClientsModal')">Close</button>
            </div>
        </div>
    </div>

        <!-- Footer -->
        <footer>
            <div class="footer-grid">
                <div class="footer-column">
                    <h3>For Clients</h3>
                    <a href="#">How to Hire</a>
                    <a href="#">Marketplace</a>
                    <a href="#">Payroll Services</a>
                    <a href="#">Service Catalog</a>
                    <a href="#">Business Networking</a>
                    <a href="#">PH Business Loan</a>
                </div>
                <div class="footer-column">
                    <h3>For Geniuses</h3>
                    <a href="#">How It Works?</a>
                    <a href="#">Why Can't I Apply?</a>
                    <a href="#">Direct Contracts</a>
                    <a href="#">Find Mentors</a>
                    <a href="#">Mentor Application</a>
                    <a href="#">PH Health Insurance</a>
                    <a href="#">PH Life Insurance</a>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <a href="#">Help & Support</a>
                    <a href="#">News & Events</a>
                    <a href="#">Affiliate Program</a>
                </div>
                <div class="footer-column">
                    <h3>Company</h3>
                    <a href="#">About Us</a>
                    <a href="#">Contact Us</a>
                    <a href="#">Charity Projects</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>
                    Follow Us:
                    <span class="social-icons">
                        <a href="https://www.facebook.com/giggenius.io"><i class="bi bi-facebook"></i></a>
                        <a href="https://www.instagram.com/giggenius.io/"><i class="bi bi-instagram"></i></a>
                        <a href="https://twitter.com/giggenius_io"><i class="bi bi-twitter-x"></i></a>
                        <a href="#"><i class="bi bi-tiktok"></i></a>
                        <a href="https://www.youtube.com/@giggenius"><i class="bi bi-youtube"></i></a>
                        <a href="https://www.linkedin.com/company/gig-genius/"><i class="bi bi-linkedin"></i></a>
                    </span>
                </p>
                <p>©2025 GigGenius by <a href="https://genuinelysolutions.com/">Genuinely Business Solutions</a></p>
                <p>
                    <a href="#">Terms of Service</a> |
                    <a href="#">Privacy Policy</a>
                </p>
            </div>
        </footer>
</body>
</html>
