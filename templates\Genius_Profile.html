<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="GigGenius - Professional freelancer profile and portfolio">
    <title>GigGenius - Profile</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #003a8c;
            --primary-pink: #d41b8c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --gray-100: #f8f9fa;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-400: #ced4da;
            --gray-500: #adb5bd;
            --gray-600: #6c757d;
            --gray-700: #495057;
            --gray-800: #343a40;
            --gray-900: #212529;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            background-color: #f8fafc;
        }

        a {
            text-decoration: none;
            color: inherit;
        }

        ul {
            list-style: none;
        }

        button, input, select, textarea {
            font-family: inherit;
            font-size: inherit;
            outline: none;
        }

        button {
            cursor: pointer;
            border: none;
            background: none;
        }

        img, video {
            max-width: 100%;
            height: auto;
        }

        /* Layout */
        .container {
            max-width: 2000px;
            margin: 0 auto;
            background-color: white;
            box-shadow: var(--shadow-sm);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .main-content {
            padding: 1.5rem 1rem 3rem;
            flex: 1;
        }

        /* Modern Navbar Styles from My_proposal.html */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 5rem;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .logo img {
            width: 3.5rem;
            height: 3.5rem;
            border-radius: 50%;
            border: 2px solid #004AAD;
            object-fit: cover;
        }

        .logo h1 {
            font-size: 1.7rem;
            font-weight: bold;
            color: #CD208B;
            margin-left: 0.5rem;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            color: #004AAD;
            text-decoration: none;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .nav-links a:hover {
            color: #CD208B;
        }

        .nav-dropdown {
            position: relative;
            display: inline-block;
        }

        .nav-dropbtn {
            font-weight: 500;
            font-size: 1.1rem;
            color: #004AAD;
            background: none;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0;
        }

        .nav-dropbtn:hover {
            color: #CD208B;
        }

        .nav-dropdown-content {
            display: none;
            position: absolute;
            background-color: #fff;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            top: 100%;
            left: 0;
        }

        .nav-dropdown-content a {
            color: #004AAD;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            font-size: 1rem;
        }

        .nav-dropdown-content a:hover {
            background-color: #f9f9f9;
            color: #CD208B;
        }

        .nav-dropdown:hover .nav-dropdown-content {
            display: block;
        }

        /* Right section container */
        .right-section {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        /* Search container */
        .search-container {
            display: flex;
            align-items: center;
        }

        .search-type-select {
            position: relative;
        }

        .search-type-button {
            height: 2.5rem;
            background: white;
            border: 1px solid #004AAD;
            border-right: none;
            border-radius: 8px 0 0 8px;
            padding: 0 1rem;
            color: #004AAD;
            font-size: 1rem;
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .search-bar {
            height: 2.5rem;
            display: flex;
            align-items: center;
            background: white;
            border: 1px solid #004AAD;
            border-radius: 0 8px 8px 0;
            width: 200px;
        }

        .search-bar input {
            border: none;
            outline: none;
            padding: 0 0.5rem;
            width: 100%;
            height: 100%;
            font-size: 1rem;
        }

        .search-bar .icon {
            color: #004AAD;
            padding: 0 0.5rem;
        }

        /* Auth buttons container */
        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        /* Notification icon */
        .notification-icon {
            position: relative;
            cursor: pointer;
        }

        .notification-icon i {
            font-size: 1.5rem;
            color: #004AAD;
        }

        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: #CD208B;
            color: white;
            border-radius: 50%;
            padding: 0.1rem 0.4rem;
            font-size: 0.7rem;
            min-width: 1.2rem;
            text-align: center;
        }

        /* Profile dropdown */
        .profile-dropdown {
            position: relative;
        }

        .profile-button {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid #004AAD;
        }

        .profile-button img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 100%;
            background-color: white;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            padding: 0.5rem 0;
            margin-top: 0.5rem;
        }

        .profile-dropdown:hover .profile-dropdown-content {
            display: block;
        }

        .profile-dropdown-content a {
            color: #004AAD;
            padding: 0.75rem 1rem;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1rem;
        }

        .profile-dropdown-content a:hover {
            background-color: #f9f9f9;
            color: #CD208B;
        }

        .dropdown-divider {
            height: 1px;
            background-color: #e5e7eb;
            margin: 0.5rem 0;
        }

        .logout-option {
            color: #dc2626 !important;
        }

        .logout-option:hover {
            background-color: #fef2f2 !important;
            color: #dc2626 !important;
        }

        /* Mobile menu */
        .mobile-menu {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background-color: white;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .mobile-menu a {
            display: block;
            padding: 0.75rem 1rem;
            color: #333;
            font-size: 0.875rem;
            border-bottom: 1px solid #f3f4f6;
        }

        .mobile-menu a:last-child {
            border-bottom: none;
        }

        .mobile-menu a:hover {
            background-color: #f9fafb;
        }

        /* Introduction Section */
        .introduction-section {
            padding: 2rem 0;
            max-width: 1200px;
            margin: 0 auto;
        }

        .introduction-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: var(--shadow-md);
        }

        .introduction-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .introduction-header h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-dark);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .introduction-content p {
            color: var(--gray-700);
            line-height: 1.6;
        }

        /* Portfolio Section */
        .portfolio-section {
            padding: 2rem 0;
            max-width: 1200px;
            margin: 0 auto;
        }

        .portfolio-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .portfolio-header h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-dark);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .add-btn {
            background: var(--primary-blue);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .add-btn:hover {
            background: #002a6b;
        }

        .portfolio-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: var(--shadow-md);
            margin-bottom: 1rem;
        }

        .portfolio-content h4 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .portfolio-description p {
            color: var(--gray-700);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
        }

        .action-edit-btn {
            background: var(--primary-pink);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .action-edit-btn:hover {
            background: #b91c77;
        }

        .delete-btn {
            background: #dc2626;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .delete-btn:hover {
            background: #b91c1c;
        }

        /* Certifications Section */
        .certifications-section {
            padding: 2rem 0;
            max-width: 1200px;
            margin: 0 auto;
        }

        .certifications-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .certifications-header h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-dark);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .certification-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: var(--shadow-md);
            margin-bottom: 1rem;
        }

        .certification-header h3 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .certification-detail {
            margin-bottom: 0.5rem;
            color: var(--gray-700);
        }

        .certification-description {
            margin: 1.5rem 0;
        }

        .certification-description h4 {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .certification-description p {
            color: var(--gray-700);
            line-height: 1.6;
        }

        /* Footer */
        footer {
            background: var(--gray-800);
            color: white;
            padding: 3rem 2rem 1rem;
            margin-top: 4rem;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .footer-column h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: white;
        }

        .footer-column a {
            display: block;
            color: var(--gray-300);
            text-decoration: none;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .footer-column a:hover {
            color: white;
        }

        .footer-bottom {
            border-top: 1px solid var(--gray-600);
            margin-top: 2rem;
            padding-top: 2rem;
            text-align: center;
            color: var(--gray-400);
            font-size: 0.875rem;
        }

        .social-icons {
            margin-left: 1rem;
        }

        .social-icons a {
            color: var(--gray-400);
            margin: 0 0.5rem;
            font-size: 1.2rem;
        }

        .social-icons a:hover {
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Modern Header Section -->
        <nav class="navbar">
            <div class="navbar-left">
                <a href="{{ url_for('landing_page') }}" class="logo">
                    <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                    <h1>GigGenius</h1>
                </a>
                <div class="nav-links">
                    <a href="{{ url_for('genius_page') }}">Find Gigs</a>
                    <a href="{{ url_for('my_proposal') }}">Proposals</a>

                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Contracts
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('tracker') }}">Log Works</a>
                            <a href="{{ url_for('landing_page') }}">Work Diary</a>
                        </div>
                    </div>

                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Earnings
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('billing_and_earnings') }}">Billings and Earnings</a>
                            <a href="{{ url_for('withdraw_earnings') }}">Withdraw Earnings</a>
                            <a href="{{ url_for('tax_info') }}">Tax Info</a>
                        </div>
                    </div>

                    <a href="{{ url_for('landing_page') }}">Messages</a>
                </div>
            </div>
            <div class="right-section">
                <div class="search-container">
                    <div class="search-type-select">
                        <button class="search-type-button">
                            <span>Gigs</span>
                        </button>
                    </div>
                    <div class="search-bar">
                        <input type="text" placeholder="Search...">
                        <i class="fas fa-search icon"></i>
                    </div>
                </div>
                <div class="auth-buttons">
                    <div class="notification-icon">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </div>
                    <div class="profile-dropdown">
                        <div class="profile-button">
                            <img src="{{ genius.profile_picture_url if genius else url_for('static', filename='img/default_profile.png') }}" alt="Profile Picture">
                        </div>
                        <div class="profile-dropdown-content">
                            <a href="{{ url_for('genius_profile') }}">
                                <i class="fas fa-user"></i> My Profile
                            </a>
                            <a href="{{ url_for('withdraw_earnings', section='profile-settings') }}">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="{{ url_for('logout') }}" class="logout-option">
                                <i class="fas fa-sign-out-alt"></i> Log Out
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <div class="mobile-menu" id="mobileMenu">
            <a href="{{ url_for('genius_page') }}">Find Gigs</a>
            <a href="{{ url_for('my_proposal') }}">Proposals</a>
            <a href="#">Contracts</a>
            <a href="{{ url_for('tracker') }}">Log Works</a>
            <a href="#">Earnings</a>
            <a href="{{ url_for('billing_and_earnings') }}">Billings and Earnings</a>
            <a href="{{ url_for('withdraw_earnings') }}">Withdraw Earnings</a>
            <a href="{{ url_for('tax_info') }}">Tax Info</a>
            <a href="{{ url_for('landing_page') }}">Messages</a>
            <a href="{{ url_for('genius_profile') }}">Profile</a>
        </div>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Profile Section -->
            <section class="profile-section">
                <div class="profile-row">
                    <!-- Video Card -->
                    <div class="video-card">
                        <div class="video-container" id="videoContainer">
                            <video id="profileVideo" controls>
                                <source src="Pictures/Video.mp4" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                            <button class="expand-btn" id="expandBtn" aria-label="Expand video">
                                <i class="fas fa-expand"></i> Fullscreen
                            </button>
                        </div>
                    </div>

                    <!-- Profile Card -->
                    <div class="profile-card">
                        <div class="profile-content">
                            <h2 class="profile-name">Nash Esguerra</h2>
                            <p class="profile-role"><i class="fas fa-briefcase"></i>Full-stack Developer</p>

                            <div class="profile-stats">
                                <div class="stat-item">
                                    <div class="stat-value">$50</div>
                                    <div class="stat-label">Hourly Rate</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">0</div>
                                    <div class="stat-label">Completed</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">$0</div>
                                    <div class="stat-label">Total Earnings</div>
                                </div>
                            </div>

                            <div class="profile-summary">
                                <h3><i class="fas fa-user-circle"></i> Professional Summary</h3>
                                <p class="summary-text" id="summaryText">Collapsible text is perfect for longer content like paragraphs and descriptions...</p>
                                <span class="show-more" id="showMore"><i class="fas fa-plus-circle"></i> Show More</span>
                                <button class="edit-btn" id="editSummaryBtn">
                                    <i class="fas fa-edit"></i>
                                    <span>Edit</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Introduction Section -->
            <section class="introduction-section">
                <div class="introduction-card">
                    <div class="introduction-header">
                        <h3><i class="fas fa-info-circle"></i> Introduction</h3>
                        <button class="edit-btn" id="editIntroductionBtn">
                            <i class="fas fa-edit"></i>
                            <span>Edit</span>
                        </button>
                    </div>
                    <div class="introduction-content">
                        <p>{{ genius.introduction if genius else 'No introduction provided.' }}</p>
                    </div>
                </div>
            </section>

            <!-- Portfolio Section -->
            <section class="portfolio-section">
                <div class="portfolio-header">
                    <h3><i class="fas fa-folder-open"></i> Portfolio</h3>
                    <button class="add-btn">
                        <i class="fas fa-plus"></i>
                        <span>Add</span>
                    </button>
                </div>

                <!-- Portfolio Card -->
                <div class="portfolio-card">
                    <div class="portfolio-header">
                        <h3><i class="fas fa-briefcase"></i> Project</h3>
                    </div>
                    <div class="portfolio-content">
                        <div class="portfolio-description">
                            <h4><i class="fas fa-align-left"></i> Description</h4>
                            <p>Collapsible text is perfect for longer content like paragraphs and descriptions. It's a great way to give people more information while keeping your layout clean.</p>
                        </div>

                        <div class="action-buttons">
                            <button class="action-edit-btn" id="portfolioEditBtn">
                                <i class="fas fa-edit"></i>
                                <span>Edit</span>
                            </button>
                            <button class="delete-btn">
                                <i class="fas fa-trash-alt"></i>
                                <span>Delete</span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Certifications Section -->
            <section class="certifications-section">
                <div class="certifications-header">
                    <h3><i class="fas fa-certificate"></i> Certifications</h3>
                    <button class="add-btn">
                        <i class="fas fa-plus"></i>
                        <span>Add</span>
                    </button>
                </div>

                <!-- Certification Card 1 -->
                <div class="certification-card">
                    <div class="certification-header">
                        <h3 class="certification-title"><i class="fas fa-certificate"></i> Certification</h3>
                    </div>
                    <div class="certification-content">
                        <div class="certification-detail">
                            <strong>Provider:</strong> Provider
                        </div>
                        <div class="certification-detail">
                            <strong>Receiver:</strong> Receiver Name
                        </div>
                        <div class="certification-detail">
                            <strong>Date of issue:</strong> [Date]
                        </div>

                        <div class="certification-description">
                            <h4><i class="fas fa-align-left"></i> Description</h4>
                            <p>Collapsible text is perfect for longer content like paragraphs and descriptions. It's a great way to give people more information while keeping your layout clean.</p>
                        </div>

                        <div class="action-buttons">
                            <button class="action-edit-btn" id="certificationEditBtn1">
                                <i class="fas fa-edit"></i>
                                <span>Edit</span>
                            </button>
                            <button class="delete-btn">
                                <i class="fas fa-trash-alt"></i>
                                <span>Delete</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Certification Card 2 -->
                <div class="certification-card">
                    <div class="certification-header">
                        <h3 class="certification-title"><i class="fas fa-certificate"></i> Certification</h3>
                    </div>
                    <div class="certification-content">
                        <div class="certification-detail">
                            <strong>Provider:</strong> Provider
                        </div>
                        <div class="certification-detail">
                            <strong>Receiver:</strong> Receiver Name
                        </div>
                        <div class="certification-detail">
                            <strong>Date of issue:</strong> [Date]
                        </div>

                        <div class="certification-description">
                            <h4><i class="fas fa-align-left"></i> Description</h4>
                            <p>Collapsible text is perfect for longer content like paragraphs and descriptions. It's a great way to give people more information while keeping your layout clean.</p>
                        </div>

                        <div class="action-buttons">
                            <button class="action-edit-btn" id="certificationEditBtn2">
                                <i class="fas fa-edit"></i>
                                <span>Edit</span>
                            </button>
                            <button class="delete-btn">
                                <i class="fas fa-trash-alt"></i>
                                <span>Delete</span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer>
            <div class="footer-grid">
                <div class="footer-column">
                    <h3>For Clients</h3>
                    <a href="#">How to Hire</a>
                    <a href="#">Marketplace</a>
                    <a href="#">Payroll Services</a>
                    <a href="#">Service Catalog</a>
                    <a href="#">Business Networking</a>
                    <a href="#">PH Business Loan</a>
                </div>
                <div class="footer-column">
                    <h3>For Geniuses</h3>
                    <a href="#">How It Works?</a>
                    <a href="#">Why Can't I Apply?</a>
                    <a href="#">Direct Contracts</a>
                    <a href="#">Find Mentors</a>
                    <a href="#">Mentor Application</a>
                    <a href="#">PH Health Insurance</a>
                    <a href="#">PH Life Insurance</a>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <a href="#">Help & Support</a>
                    <a href="#">News & Events</a>
                    <a href="#">Affiliate Program</a>
                </div>
                <div class="footer-column">
                    <h3>Company</h3>
                    <a href="#">About Us</a>
                    <a href="#">Contact Us</a>
                    <a href="#">Charity Projects</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>
                    Follow Us:
                    <span class="social-icons">
                        <a href="https://www.facebook.com/giggenius.io"><i class="bi bi-facebook"></i></a>
                        <a href="https://www.instagram.com/giggenius.io/"><i class="bi bi-instagram"></i></a>
                        <a href="https://twitter.com/giggenius_io"><i class="bi bi-twitter-x"></i></a>
                        <a href="#"><i class="bi bi-tiktok"></i></a>
                        <a href="https://www.youtube.com/@giggenius"><i class="bi bi-youtube"></i></a>
                        <a href="https://www.linkedin.com/company/gig-genius/"><i class="bi bi-linkedin"></i></a>
                    </span>
                </p>
                <p>©2025 GigGenius by <a href="https://genuinelysolutions.com/">Genuinely Business Solutions</a></p>
                <p>
                    <a href="#">Terms of Service</a> |
                    <a href="#">Privacy Policy</a>
                </p>
            </div>
        </footer>
    </div>

    <!-- JavaScript -->
    <script>
        // Show More functionality for summary text
        const summaryText = document.getElementById('summaryText');
        const showMoreBtn = document.getElementById('showMore');
        const editSummaryBtn = document.getElementById('editSummaryBtn');

        if (showMoreBtn) {
            showMoreBtn.addEventListener('click', () => {
                summaryText.textContent = "Collapsible text is perfect for longer content like paragraphs and descriptions. It's a great way to give people more information while keeping your layout clean.";
                showMoreBtn.style.display = 'none';
            });
        }

        // Video expand/collapse functionality
        const videoContainer = document.getElementById('videoContainer');
        const expandBtn = document.getElementById('expandBtn');
        const video = document.getElementById('profileVideo');

        if (expandBtn && videoContainer && video) {
            expandBtn.addEventListener('click', () => {
                videoContainer.classList.toggle('expanded');
                if (videoContainer.classList.contains('expanded')) {
                    expandBtn.innerHTML = '<i class="fas fa-compress"></i> Exit Fullscreen';
                    document.body.style.overflow = 'hidden';
                } else {
                    expandBtn.innerHTML = '<i class="fas fa-expand"></i> Fullscreen';
                    document.body.style.overflow = '';
                }
            });

            // Close expanded video when clicking outside
            document.addEventListener('click', (event) => {
                if (videoContainer.classList.contains('expanded') &&
                    !video.contains(event.target) &&
                    !expandBtn.contains(event.target)) {
                    videoContainer.classList.remove('expanded');
                    expandBtn.innerHTML = '<i class="fas fa-expand"></i> Fullscreen';
                    document.body.style.overflow = '';
                }
            });

            // Close expanded video when pressing Escape key
            document.addEventListener('keydown', (event) => {
                if (event.key === 'Escape' && videoContainer.classList.contains('expanded')) {
                    videoContainer.classList.remove('expanded');
                    expandBtn.innerHTML = '<i class="fas fa-expand"></i> Fullscreen';
                    document.body.style.overflow = '';
                }
            });
        }
    </script>
</body>
</html>
